.class public final Lcom/abox/apps/activitys/PayActivity$TaskDescription;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lo/OptionalDataException;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/abox/apps/activitys/PayActivity;->onCreate(Landroid/os/Bundle;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lo/OptionalDataException<",
        "Lcom/abox/apps/model/OrderResult;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000%\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0004*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\"\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u00062\u0008\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0012\u0010\u000c\u001a\u00020\u00042\u0008\u0010\r\u001a\u0004\u0018\u00010\u0002H\u0016\u00a8\u0006\u000e"
    }
    d2 = {
        "com/abox/apps/activitys/PayActivity$onCreate$1",
        "Lcom/abox/apps/pay/ICommonCallback;",
        "Lcom/abox/apps/model/OrderResult;",
        "onCanceled",
        "",
        "type",
        "",
        "onFailed",
        "code",
        "message",
        "",
        "onStart",
        "onSucceed",
        "t",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic asInterface:Lo/PipedInputStream;

.field final synthetic setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;


# direct methods
.method constructor <init>(Lcom/abox/apps/activitys/PayActivity;Lo/PipedInputStream;)V
    .locals 0

    iput-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    iput-object p2, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->asInterface:Lo/PipedInputStream;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public asBinder(ILjava/lang/String;I)V
    .locals 1
    .param p2    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    invoke-static {p0, p1, p2, p3}, Lo/OptionalDataException$StateListAnimator;->setDefaultImpl(Lo/OptionalDataException;ILjava/lang/String;I)V

    sget-object p3, Lo/Animation;->getDefaultImpl:Lo/Animation;

    invoke-virtual {p3}, Lo/Animation;->onTransact()V

    const/4 p3, 0x7

    if-ne p1, p3, :cond_0

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    const/16 p2, 0xc9

    invoke-virtual {p1, p2}, Landroid/app/Activity;->setResult(I)V

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    new-instance p3, Landroid/content/Intent;

    invoke-direct {p3}, Landroid/content/Intent;-><init>()V

    const-string v0, "message_key"

    invoke-virtual {p3, v0, p2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    sget-object p2, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    const/16 p2, 0xca

    invoke-virtual {p1, p2, p3}, Landroid/app/Activity;->setResult(ILandroid/content/Intent;)V

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    :goto_0
    return-void
.end method

.method public asInterface(I)V
    .locals 1

    invoke-static {p0, p1}, Lo/OptionalDataException$StateListAnimator;->setDefaultImpl(Lo/OptionalDataException;I)V

    sget-object p1, Lo/Animation;->getDefaultImpl:Lo/Animation;

    invoke-virtual {p1}, Lo/Animation;->onTransact()V

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    const/16 v0, 0xc9

    invoke-virtual {p1, v0}, Landroid/app/Activity;->setResult(I)V

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public synthetic asInterface(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Lcom/abox/apps/model/OrderResult;

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl(Lcom/abox/apps/model/OrderResult;)V

    return-void
.end method

.method public getDefaultImpl(I)V
    .locals 3

    invoke-static {p0, p1}, Lo/OptionalDataException$StateListAnimator;->onTransact(Lo/OptionalDataException;I)V

    const/16 v0, 0x64

    if-ne p1, v0, :cond_0

    sget-object p1, Lo/Animation;->getDefaultImpl:Lo/Animation;

    iget-object v0, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    const/4 v1, 0x2

    const/4 v2, 0x0

    invoke-static {p1, v0, v2, v1, v2}, Lo/Animation;->getDefaultImpl(Lo/Animation;Landroid/content/Context;Ljava/lang/String;ILjava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public setDefaultImpl(Lcom/abox/apps/model/OrderResult;)V
    .locals 3
    .param p1    # Lcom/abox/apps/model/OrderResult;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    invoke-static {p0, p1}, Lo/OptionalDataException$StateListAnimator;->asBinder(Lo/OptionalDataException;Ljava/lang/Object;)V

    if-nez p1, :cond_0

    return-void

    :cond_0
    instance-of v0, p1, Lcom/abox/apps/model/OrderResult$CreateOrderResult;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->asInterface:Lo/PipedInputStream;

    iget-object v1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    invoke-virtual {v1}, Lcom/abox/apps/activitys/PayActivity;->setDefaultImpl()Lo/OptionalDataException;

    move-result-object v2

    invoke-virtual {v0, v1, p1, v2}, Lo/PipedInputStream;->setDefaultImpl(Landroidx/fragment/app/FragmentActivity;Lcom/abox/apps/model/OrderResult;Lo/OptionalDataException;)V

    goto :goto_1

    :cond_1
    instance-of v0, p1, Lcom/abox/apps/model/OrderResult$VerifyOrderResult;

    if-eqz v0, :cond_3

    sget-object v0, Lo/Animation;->getDefaultImpl:Lo/Animation;

    invoke-virtual {v0}, Lo/Animation;->onTransact()V

    iget-object v0, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    new-instance v1, Landroid/content/Intent;

    invoke-direct {v1}, Landroid/content/Intent;-><init>()V

    check-cast p1, Lcom/abox/apps/model/OrderResult$VerifyOrderResult;

    invoke-virtual {p1}, Lcom/abox/apps/model/OrderResult$VerifyOrderResult;->getOrderRes()Lcom/abox/apps/model/VerifyOrderRes;

    move-result-object p1

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/abox/apps/model/VerifyOrderRes;->getUser()Lcom/abox/apps/model/UserInfo;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    const-string/jumbo v2, "user_info_key"

    invoke-virtual {v1, v2, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Landroid/os/Parcelable;)Landroid/content/Intent;

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    const/16 p1, 0xc8

    invoke-virtual {v0, p1, v1}, Landroid/app/Activity;->setResult(ILandroid/content/Intent;)V

    iget-object p1, p0, Lcom/abox/apps/activitys/PayActivity$TaskDescription;->setDefaultImpl:Lcom/abox/apps/activitys/PayActivity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    :cond_3
    :goto_1
    return-void
.end method
