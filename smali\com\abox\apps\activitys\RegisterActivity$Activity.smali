.class final Lcom/abox/apps/activitys/RegisterActivity$Activity;
.super Lkotlin/jvm/internal/Lambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/abox/apps/activitys/RegisterActivity;->onTransact()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lcom/abox/apps/model/BaseResponse<",
        "Lcom/abox/apps/model/UserInfo;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u00012\u0010\u0010\u0002\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0018\u00010\u0003H\n\u00a2\u0006\u0002\u0008\u0005"
    }
    d2 = {
        "<anonymous>",
        "",
        "it",
        "Lcom/abox/apps/model/BaseResponse;",
        "Lcom/abox/apps/model/UserInfo;",
        "invoke"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic onTransact:Lcom/abox/apps/activitys/RegisterActivity;


# direct methods
.method constructor <init>(Lcom/abox/apps/activitys/RegisterActivity;)V
    .locals 0

    iput-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final asBinder(Lcom/abox/apps/model/BaseResponse;)V
    .locals 4
    .param p1    # Lcom/abox/apps/model/BaseResponse;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/abox/apps/model/BaseResponse<",
            "Lcom/abox/apps/model/UserInfo;",
            ">;)V"
        }
    .end annotation

    sget-object v0, Lo/Animation;->getDefaultImpl:Lo/Animation;

    invoke-virtual {v0}, Lo/Animation;->onTransact()V

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/abox/apps/model/BaseResponse;->isSucceed()Z

    move-result v2

    if-ne v0, v2, :cond_0

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    const/4 v2, 0x0

    if-eqz v0, :cond_1

    sget-object v0, Lo/PrintWriter;->setDefaultImpl:Lo/PrintWriter;

    invoke-virtual {p1}, Lcom/abox/apps/model/BaseResponse;->getData()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/model/UserInfo;

    invoke-virtual {v0, p1}, Lo/PrintWriter;->asInterface(Lcom/abox/apps/model/UserInfo;)V

    iget-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    const/4 v0, -0x1

    invoke-virtual {p1, v0}, Landroid/app/Activity;->setResult(I)V

    iget-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    invoke-virtual {p1}, Landroid/app/Activity;->finish()V

    iget-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    sget v0, Lo/Cursor$TaskStackBuilder;->getDefaultImpl:I

    const/4 v3, 0x2

    invoke-static {p1, v0, v1, v3, v2}, Lo/FilePermission;->asInterface(Landroid/content/Context;IIILjava/lang/Object;)V

    goto :goto_1

    :cond_1
    iget-object v0, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    invoke-virtual {v0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->IconCompatParcelizer:Landroid/widget/TextView;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/abox/apps/model/BaseResponse;->getMsg()Ljava/lang/String;

    move-result-object v2

    :cond_2
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity$Activity;->onTransact:Lcom/abox/apps/activitys/RegisterActivity;

    invoke-virtual {p1}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->IconCompatParcelizer:Landroid/widget/TextView;

    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    :goto_1
    return-void
.end method

.method public synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/abox/apps/model/BaseResponse;

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity$Activity;->asBinder(Lcom/abox/apps/model/BaseResponse;)V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1
.end method
