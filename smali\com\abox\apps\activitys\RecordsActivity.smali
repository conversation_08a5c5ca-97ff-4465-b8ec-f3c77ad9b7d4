.class public final Lcom/abox/apps/activitys/RecordsActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/activitys/RecordsActivity$Application;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u0000 \u00192\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0019B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000f\u001a\u00020\u0010H\u0014J\u0008\u0010\u0011\u001a\u00020\u0012H\u0014J\u0008\u0010\u0013\u001a\u00020\u0012H\u0014J \u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00062\u000e\u0010\u0016\u001a\n\u0012\u0002\u0008\u0003\u0012\u0002\u0008\u00030\u0017H\u0002J\u0008\u0010\u0018\u001a\u00020\u0012H\u0002R\u001e\u0010\u0004\u001a\u0012\u0012\u0004\u0012\u00020\u00060\u0005j\u0008\u0012\u0004\u0012\u00020\u0006`\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0008\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000c\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"
    }
    d2 = {
        "Lcom/abox/apps/activitys/RecordsActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;",
        "()V",
        "dataList",
        "Ljava/util/ArrayList;",
        "Lcom/abox/apps/model/RecordInfo;",
        "Lkotlin/collections/ArrayList;",
        "deleteDialog",
        "Landroid/app/Dialog;",
        "lastClickTime",
        "",
        "recordAdapter",
        "Lcom/abox/apps/adapters/RecordAdapter;",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "",
        "onDestroy",
        "showDeleteDialog",
        "item",
        "adapter",
        "Lcom/chad/library/adapter/base/BaseQuickAdapter;",
        "updateLayout",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nRecordsActivity.kt\nKotlin\n*S Kotlin\n*F\n+ 1 RecordsActivity.kt\ncom/abox/apps/activitys/RecordsActivity\n+ 2 View.kt\nandroidx/core/view/ViewKt\n*L\n1#1,121:1\n262#2,2:122\n262#2,2:124\n*S KotlinDebug\n*F\n+ 1 RecordsActivity.kt\ncom/abox/apps/activitys/RecordsActivity\n*L\n112#1:122,2\n113#1:124,2\n*E\n"
    }
.end annotation


# static fields
.field private static final asBinder:J = 0x258L

.field public static final getDefaultImpl:Lcom/abox/apps/activitys/RecordsActivity$Application;
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field private IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

.field private asInterface:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/abox/apps/model/RecordInfo;",
            ">;"
        }
    .end annotation
.end field

.field private setDefaultImpl:Landroid/app/Dialog;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private write:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/activitys/RecordsActivity$Application;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/activitys/RecordsActivity$Application;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/activitys/RecordsActivity;->getDefaultImpl:Lcom/abox/apps/activitys/RecordsActivity$Application;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    return-void
.end method

.method public static final synthetic asBinder(Lcom/abox/apps/activitys/RecordsActivity;)Lcom/abox/apps/adapters/RecordAdapter;
    .locals 0

    iget-object p0, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    return-object p0
.end method

.method private static final asBinder(Lcom/abox/apps/activitys/RecordsActivity;Landroid/view/View;)V
    .locals 0

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public static final synthetic asBinder(Lcom/abox/apps/activitys/RecordsActivity;Ljava/util/ArrayList;)V
    .locals 0

    iput-object p1, p0, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    return-void
.end method

.method public static synthetic asBinder(Lcom/abox/apps/model/RecordInfo;Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/content/DialogInterface;I)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lcom/abox/apps/activitys/RecordsActivity;->asInterface(Lcom/abox/apps/model/RecordInfo;Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/content/DialogInterface;I)V

    return-void
.end method

.method public static synthetic asInterface(Lcom/abox/apps/activitys/RecordsActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecordsActivity;->asBinder(Lcom/abox/apps/activitys/RecordsActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final asInterface(Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/view/View;I)V
    .locals 4

    const-string/jumbo v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "adapter"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string/jumbo v0, "view"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/abox/apps/activitys/RecordsActivity;->write:J

    sub-long/2addr v0, v2

    const-wide/16 v2, 0x258

    cmp-long v0, v0, v2

    if-gez v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->write:J

    iget-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    if-nez v0, :cond_1

    const-string v0, "dataList"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    const/4 v0, 0x0

    :cond_1
    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p3

    const-string v0, "get(...)"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p3, Lcom/abox/apps/model/RecordInfo;

    invoke-virtual {p2}, Landroid/view/View;->getId()I

    move-result p2

    sget v0, Lo/Cursor$LoaderManager;->R:I

    if-ne p2, v0, :cond_2

    sget-object p1, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string/jumbo p2, "shareFilesPage"

    invoke-virtual {p1, p2}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object p1, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    invoke-virtual {p1, p2}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    sget-object p1, Lo/UnicodeScript;->setDefaultImpl:Lo/UnicodeScript;

    invoke-virtual {p3}, Lcom/abox/apps/model/RecordInfo;->getFilePath()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p0, p2}, Lo/UnicodeScript;->setDefaultImpl(Landroid/content/Context;Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    sget v0, Lo/Cursor$LoaderManager;->ComponentActivity:I

    if-ne p2, v0, :cond_3

    invoke-direct {p0, p3, p1}, Lcom/abox/apps/activitys/RecordsActivity;->setDefaultImpl(Lcom/abox/apps/model/RecordInfo;Lcom/chad/library/adapter/base/BaseQuickAdapter;)V

    :cond_3
    :goto_0
    return-void
.end method

.method private static final asInterface(Lcom/abox/apps/model/RecordInfo;Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/content/DialogInterface;I)V
    .locals 1

    const-string v0, "$item"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string/jumbo v0, "this$0"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "$adapter"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, -0x2

    if-eq p4, v0, :cond_3

    const/4 v0, -0x1

    if-eq p4, v0, :cond_0

    goto :goto_0

    :cond_0
    if-eqz p3, :cond_1

    invoke-interface {p3}, Landroid/content/DialogInterface;->dismiss()V

    :cond_1
    sget-object p3, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string p4, "deleteRecording"

    invoke-virtual {p3, p4}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object p3, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    invoke-virtual {p3, p4}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    sget-object p3, Lo/BootstrapMethodError;->asInterface:Lo/BootstrapMethodError;

    invoke-virtual {p3, p0}, Lo/BootstrapMethodError;->asBinder(Lcom/abox/apps/model/RecordInfo;)V

    iget-object p3, p1, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    if-nez p3, :cond_2

    const-string p3, "dataList"

    invoke-static {p3}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    const/4 p3, 0x0

    :cond_2
    invoke-virtual {p3, p0}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    invoke-direct {p1}, Lcom/abox/apps/activitys/RecordsActivity;->setDefaultImpl()V

    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyDataSetChanged()V

    goto :goto_0

    :cond_3
    if-eqz p3, :cond_4

    invoke-interface {p3}, Landroid/content/DialogInterface;->dismiss()V

    :cond_4
    :goto_0
    return-void
.end method

.method public static final synthetic onTransact(Lcom/abox/apps/activitys/RecordsActivity;)V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/RecordsActivity;->setDefaultImpl()V

    return-void
.end method

.method public static final synthetic setDefaultImpl(Lcom/abox/apps/activitys/RecordsActivity;)Ljava/util/ArrayList;
    .locals 0

    iget-object p0, p0, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    return-object p0
.end method

.method private final setDefaultImpl()V
    .locals 6

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->setDefaultImpl:Landroid/widget/TextView;

    const-string v1, "emptyTip"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v1, p0, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    const-string v2, "dataList"

    const/4 v3, 0x0

    if-nez v1, :cond_0

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v1, v3

    :cond_0
    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v1

    const/16 v4, 0x8

    const/4 v5, 0x0

    if-eqz v1, :cond_1

    move v1, v5

    goto :goto_0

    :cond_1
    move v1, v4

    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->getDefaultImpl:Landroidx/recyclerview/widget/RecyclerView;

    const-string v1, "recordRv"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v1, p0, Lcom/abox/apps/activitys/RecordsActivity;->asInterface:Ljava/util/ArrayList;

    if-nez v1, :cond_2

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    move-object v3, v1

    :goto_1
    invoke-interface {v3}, Ljava/util/Collection;->isEmpty()Z

    move-result v1

    xor-int/lit8 v1, v1, 0x1

    if-eqz v1, :cond_3

    move v4, v5

    :cond_3
    invoke-virtual {v0, v4}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public static synthetic setDefaultImpl(Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/view/View;I)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lcom/abox/apps/activitys/RecordsActivity;->asInterface(Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;Landroid/view/View;I)V

    return-void
.end method

.method private final setDefaultImpl(Lcom/abox/apps/model/RecordInfo;Lcom/chad/library/adapter/base/BaseQuickAdapter;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/abox/apps/model/RecordInfo;",
            "Lcom/chad/library/adapter/base/BaseQuickAdapter<",
            "**>;)V"
        }
    .end annotation

    new-instance v0, Lcom/abox/apps/dialog/DeleteRecordFileDialogKit;

    invoke-direct {v0, p0}, Lcom/abox/apps/dialog/DeleteRecordFileDialogKit;-><init>(Landroid/content/Context;)V

    new-instance v1, Lo/Size;

    invoke-direct {v1, p1, p0, p2}, Lo/Size;-><init>(Lcom/abox/apps/model/RecordInfo;Lcom/abox/apps/activitys/RecordsActivity;Lcom/chad/library/adapter/base/BaseQuickAdapter;)V

    invoke-virtual {v0, v1}, Lcom/abox/apps/dialog/DeleteRecordFileDialogKit;->getDefaultImpl(Landroid/content/DialogInterface$OnClickListener;)Lcom/abox/apps/dialog/DeleteRecordFileDialogKit;

    move-result-object p1

    iput-object p1, p0, Lcom/abox/apps/activitys/RecordsActivity;->setDefaultImpl:Landroid/app/Dialog;

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Landroid/app/Dialog;->show()V

    return-void
.end method


# virtual methods
.method public onDestroy()V
    .locals 1

    invoke-super {p0}, Landroidx/appcompat/app/AppCompatActivity;->onDestroy()V

    iget-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->setDefaultImpl:Landroid/app/Dialog;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/app/Dialog;->dismiss()V

    :cond_0
    iget-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    if-nez v0, :cond_1

    const-string v0, "recordAdapter"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    const/4 v0, 0x0

    :cond_1
    invoke-virtual {v0}, Lcom/abox/apps/adapters/RecordAdapter;->setDefaultImpl()V

    return-void
.end method

.method protected onTransact(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;
    .locals 1
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1}, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->asBinder(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method protected onTransact()V
    .locals 10

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string v1, "enterFilesPage"

    invoke-virtual {v0, v1}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    invoke-virtual {v0, v1}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->asBinder:Landroid/widget/ImageView;

    new-instance v1, Lo/SizeF;

    invoke-direct {v1, p0}, Lo/SizeF;-><init>(Lcom/abox/apps/activitys/RecordsActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->getDefaultImpl:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v1, Landroidx/recyclerview/widget/LinearLayoutManager;

    invoke-direct {v1, p0}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    new-instance v0, Lcom/abox/apps/adapters/RecordAdapter;

    invoke-direct {v0}, Lcom/abox/apps/adapters/RecordAdapter;-><init>()V

    iput-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;->getDefaultImpl:Landroidx/recyclerview/widget/RecyclerView;

    iget-object v1, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    const-string v2, "recordAdapter"

    const/4 v3, 0x0

    if-nez v1, :cond_0

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v1, v3

    :cond_0
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    iget-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    if-nez v0, :cond_1

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v0, v3

    :cond_1
    const/4 v1, 0x3

    new-array v1, v1, [I

    const/4 v4, 0x0

    sget v5, Lo/Cursor$LoaderManager;->FullyDrawnReporterOwner:I

    aput v5, v1, v4

    const/4 v4, 0x1

    sget v5, Lo/Cursor$LoaderManager;->R:I

    aput v5, v1, v4

    const/4 v4, 0x2

    sget v5, Lo/Cursor$LoaderManager;->ComponentActivity:I

    aput v5, v1, v4

    invoke-virtual {v0, v1}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->asBinder([I)V

    iget-object v0, p0, Lcom/abox/apps/activitys/RecordsActivity;->IconCompatParcelizer:Lcom/abox/apps/adapters/RecordAdapter;

    if-nez v0, :cond_2

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v0, v3

    :cond_2
    new-instance v1, Lo/SparseArray;

    invoke-direct {v1, p0}, Lo/SparseArray;-><init>(Lcom/abox/apps/activitys/RecordsActivity;)V

    invoke-virtual {v0, v1}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->asBinder(Lo/TransactionTooLargeException;)V

    invoke-static {p0}, Landroidx/lifecycle/LifecycleOwnerKt;->getLifecycleScope(Landroidx/lifecycle/LifecycleOwner;)Landroidx/lifecycle/LifecycleCoroutineScope;

    move-result-object v4

    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getIO()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v5

    const/4 v6, 0x0

    new-instance v7, Lcom/abox/apps/activitys/RecordsActivity$Activity;

    invoke-direct {v7, p0, v3}, Lcom/abox/apps/activitys/RecordsActivity$Activity;-><init>(Lcom/abox/apps/activitys/RecordsActivity;Lkotlin/coroutines/Continuation;)V

    const/4 v8, 0x2

    const/4 v9, 0x0

    invoke-static/range {v4 .. v9}, Lkotlinx/coroutines/BuildersKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/RecordsActivity;->onTransact(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivitySavedRecordsBinding;

    move-result-object p1

    return-object p1
.end method
