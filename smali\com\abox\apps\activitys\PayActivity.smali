.class public final Lcom/abox/apps/activitys/PayActivity;
.super Landroidx/appcompat/app/AppCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/activitys/PayActivity$StateListAnimator;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0018\u0000 \u00112\u00020\u0001:\u0001\u0011B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\n\u001a\u00020\u000b2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\rH\u0014J\u0012\u0010\u000e\u001a\u00020\u000b2\u0008\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0014R \u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007\"\u0004\u0008\u0008\u0010\t\u00a8\u0006\u0012"
    }
    d2 = {
        "Lcom/abox/apps/activitys/PayActivity;",
        "Landroidx/appcompat/app/AppCompatActivity;",
        "()V",
        "callback",
        "Lcom/abox/apps/pay/ICommonCallback;",
        "Lcom/abox/apps/model/OrderResult;",
        "getCallback",
        "()Lcom/abox/apps/pay/ICommonCallback;",
        "setCallback",
        "(Lcom/abox/apps/pay/ICommonCallback;)V",
        "onCreate",
        "",
        "savedInstanceState",
        "Landroid/os/Bundle;",
        "onNewIntent",
        "newIntent",
        "Landroid/content/Intent;",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final asBinder:Ljava/lang/String; = "message_key"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final asInterface:Lcom/abox/apps/activitys/PayActivity$StateListAnimator;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final getDefaultImpl:Ljava/lang/String; = "shop_id_key"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final onTransact:Ljava/lang/String; = "google_shop_id_key"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final setDefaultImpl:Ljava/lang/String; = "user_info_key"
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field public IconCompatParcelizer:Lo/OptionalDataException;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lo/OptionalDataException<",
            "Lcom/abox/apps/model/OrderResult;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/activitys/PayActivity$StateListAnimator;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/activitys/PayActivity$StateListAnimator;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/activitys/PayActivity;->asInterface:Lcom/abox/apps/activitys/PayActivity$StateListAnimator;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroidx/appcompat/app/AppCompatActivity;-><init>()V

    return-void
.end method


# virtual methods
.method public onCreate(Landroid/os/Bundle;)V
    .locals 12
    .param p1    # Landroid/os/Bundle;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    invoke-super {p0, p1}, Landroidx/fragment/app/FragmentActivity;->onCreate(Landroid/os/Bundle;)V

    sget-object p1, Lo/SequenceInputStream;->getDefaultImpl:Lo/SequenceInputStream;

    invoke-virtual {p1}, Lo/SequenceInputStream;->getDefaultImpl()Ljava/lang/String;

    move-result-object p1

    const-string v0, "googlePlay"

    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    sget-object v0, Lo/PipedWriter;->asBinder:Lo/PipedWriter;

    const/4 v1, 0x3

    if-eqz p1, :cond_0

    const/4 v2, 0x5

    goto :goto_0

    :cond_0
    move v2, v1

    :goto_0
    invoke-virtual {v0, v2}, Lo/PipedWriter;->asBinder(I)Lo/PipedInputStream;

    move-result-object v0

    new-instance v2, Lcom/abox/apps/activitys/PayActivity$TaskDescription;

    invoke-direct {v2, p0, v0}, Lcom/abox/apps/activitys/PayActivity$TaskDescription;-><init>(Lcom/abox/apps/activitys/PayActivity;Lo/PipedInputStream;)V

    invoke-virtual {p0, v2}, Lcom/abox/apps/activitys/PayActivity;->setDefaultImpl(Lo/OptionalDataException;)V

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v2

    const/4 v3, -0x1

    const-string/jumbo v4, "shop_id_key"

    invoke-virtual {v2, v4, v3}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result v7

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v2

    const-string v3, "google_shop_id_key"

    invoke-virtual {v2, v3}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/4 v1, 0x2

    :goto_1
    move v6, v1

    new-instance p1, Lcom/abox/apps/model/CreateOrderReq;

    new-instance v1, Lcom/abox/apps/model/ShopOrder;

    const/4 v9, 0x0

    const/16 v10, 0x8

    const/4 v11, 0x0

    move-object v5, v1

    invoke-direct/range {v5 .. v11}, Lcom/abox/apps/model/ShopOrder;-><init>(IILjava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    invoke-direct {p1, v1}, Lcom/abox/apps/model/CreateOrderReq;-><init>(Lcom/abox/apps/model/ShopOrder;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/PayActivity;->setDefaultImpl()Lo/OptionalDataException;

    move-result-object v1

    invoke-virtual {v0, p0, p1, v1}, Lo/PipedInputStream;->setDefaultImpl(Landroidx/fragment/app/FragmentActivity;Lcom/abox/apps/model/CreateOrderReq;Lo/OptionalDataException;)V

    return-void
.end method

.method public onNewIntent(Landroid/content/Intent;)V
    .locals 1
    .param p1    # Landroid/content/Intent;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v0

    invoke-super {p0, v0}, Landroidx/activity/ComponentActivity;->onNewIntent(Landroid/content/Intent;)V

    invoke-virtual {p0, p1}, Landroid/app/Activity;->setIntent(Landroid/content/Intent;)V

    return-void
.end method

.method public final setDefaultImpl()Lo/OptionalDataException;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lo/OptionalDataException<",
            "Lcom/abox/apps/model/OrderResult;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/activitys/PayActivity;->IconCompatParcelizer:Lo/OptionalDataException;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    const-string v0, "callback"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    const/4 v0, 0x0

    return-object v0
.end method

.method public final setDefaultImpl(Lo/OptionalDataException;)V
    .locals 1
    .param p1    # Lo/OptionalDataException;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lo/OptionalDataException<",
            "Lcom/abox/apps/model/OrderResult;",
            ">;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lcom/abox/apps/activitys/PayActivity;->IconCompatParcelizer:Lo/OptionalDataException;

    return-void
.end method
