.class public final Lo/UnlockClickListener;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field private final activity:Lcom/abox/apps/activitys/UnlockActivity;


# direct methods
.method public constructor <init>(Lcom/abox/apps/activitys/UnlockActivity;)V
    .locals 1
    .param p1    # Lcom/abox/apps/activitys/UnlockActivity;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string v0, "activity"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo/UnlockClickListener;->activity:Lcom/abox/apps/activitys/UnlockActivity;

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1
    .param p1    # Landroid/view/View;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iget-object v0, p0, Lo/UnlockClickListener;->activity:Lcom/abox/apps/activitys/UnlockActivity;

    invoke-static {v0, p1}, Lcom/abox/apps/activitys/UnlockActivity;->access$onUnlockButtonClick(Lcom/abox/apps/activitys/UnlockActivity;Landroid/view/View;)V

    return-void
.end method
