.class public final Lo/WriteAbortedException;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo/WriteAbortedException$Application;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\u0018\u0000 %2\u00020\u0001:\u0001%B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0017\u001a\u00020\u0018J \u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J\u0018\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J \u0010\u001e\u001a\u00020\u00182\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J\u0018\u0010!\u001a\u00020\u00182\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J&\u0010\"\u001a\u00020\u00182\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010#\u001a\u00020$R\u001a\u0010\u0003\u001a\u00020\u0004X\u0086.\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0005\u0010\u0006\"\u0004\u0008\u0007\u0010\u0008R\u001c\u0010\t\u001a\u0004\u0018\u00010\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u000b\u0010\u000c\"\u0004\u0008\r\u0010\u000eR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0012\u0010\u0013\"\u0004\u0008\u0014\u0010\u0015R\u000e\u0010\u0016\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"
    }
    d2 = {
        "Lcom/abox/apps/utils/LoginAndPremiumViewUtil;",
        "",
        "()V",
        "binding",
        "Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;",
        "getBinding",
        "()Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;",
        "setBinding",
        "(Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;)V",
        "dialog",
        "Landroid/app/Dialog;",
        "getDialog",
        "()Landroid/app/Dialog;",
        "setDialog",
        "(Landroid/app/Dialog;)V",
        "showWidth",
        "",
        "type",
        "getType",
        "()I",
        "setType",
        "(I)V",
        "voiceId",
        "dismiss",
        "",
        "initView",
        "rootView",
        "Landroid/view/View;",
        "voiceInfo",
        "Lcom/abox/apps/model/VoiceInfo;",
        "showDialog",
        "context",
        "Landroid/content/Context;",
        "showFloatView",
        "showView",
        "fromFloatBall",
        "",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nLoginAndPremiumViewUtil.kt\nKotlin\n*S Kotlin\n*F\n+ 1 LoginAndPremiumViewUtil.kt\ncom/abox/apps/utils/LoginAndPremiumViewUtil\n+ 2 View.kt\nandroidx/core/view/ViewKt\n*L\n1#1,155:1\n262#2,2:156\n*S KotlinDebug\n*F\n+ 1 LoginAndPremiumViewUtil.kt\ncom/abox/apps/utils/LoginAndPremiumViewUtil\n*L\n93#1:156,2\n*E\n"
    }
.end annotation


# static fields
.field public static final asBinder:I = 0x1

.field public static final asInterface:Lo/WriteAbortedException$Application;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final getDefaultImpl:I = 0x2

.field public static final onTransact:Ljava/lang/String; = "LOGIN_TAG"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final setDefaultImpl:Ljava/lang/String; = "UPGRADE_TAG"
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field private IconCompatParcelizer:Landroid/app/Dialog;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private RemoteActionCompatParcelizer:I

.field private onConnected:I

.field public read:Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

.field private write:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lo/WriteAbortedException$Application;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lo/WriteAbortedException$Application;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lo/WriteAbortedException;->asInterface:Lo/WriteAbortedException$Application;

    return-void
.end method

.method public constructor <init>()V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput v0, p0, Lo/WriteAbortedException;->RemoteActionCompatParcelizer:I

    sget-object v0, Lo/ViewPropertyAnimatorRT;->asBinder:Lo/ViewPropertyAnimatorRT;

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object v1

    const-string v2, "getContext(...)"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lo/ViewPropertyAnimatorRT;->onTransact(Landroid/content/Context;)I

    move-result v1

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object v3

    invoke-static {v3, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    const/high16 v2, 0x42400000    # 48.0f

    invoke-virtual {v0, v3, v2}, Lo/ViewPropertyAnimatorRT;->asBinder(Landroid/content/Context;F)I

    move-result v0

    mul-int/lit8 v0, v0, 0x2

    sub-int/2addr v1, v0

    iput v1, p0, Lo/WriteAbortedException;->write:I

    return-void
.end method

.method private final asBinder(Landroid/content/Context;Lcom/abox/apps/model/VoiceInfo;I)V
    .locals 1

    invoke-direct {p0, p2, p3}, Lo/WriteAbortedException;->setDefaultImpl(Lcom/abox/apps/model/VoiceInfo;I)V

    new-instance p2, Landroidx/appcompat/app/AppCompatDialog;

    sget p3, Lo/Cursor$PictureInPictureParams;->write:I

    invoke-direct {p2, p1, p3}, Landroidx/appcompat/app/AppCompatDialog;-><init>(Landroid/content/Context;I)V

    invoke-virtual {p0}, Lo/WriteAbortedException;->asBinder()Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

    move-result-object p1

    invoke-virtual {p1}, Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;->setDefaultImpl()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroidx/appcompat/app/AppCompatDialog;->setContentView(Landroid/view/View;)V

    invoke-virtual {p2}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    move-result-object p1

    if-eqz p1, :cond_0

    iget p3, p0, Lo/WriteAbortedException;->write:I

    const/4 v0, -0x2

    invoke-virtual {p1, p3, v0}, Landroid/view/Window;->setLayout(II)V

    :cond_0
    const/4 p1, 0x0

    invoke-virtual {p2, p1}, Landroid/app/Dialog;->setCancelable(Z)V

    invoke-virtual {p2}, Landroid/app/Dialog;->show()V

    iput-object p2, p0, Lo/WriteAbortedException;->IconCompatParcelizer:Landroid/app/Dialog;

    return-void
.end method

.method public static synthetic asBinder(Lo/WriteAbortedException;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lo/WriteAbortedException;->asInterface(Lo/WriteAbortedException;Landroid/view/View;)V

    return-void
.end method

.method private static final asInterface(Lo/WriteAbortedException;Landroid/view/View;)V
    .locals 0

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lo/WriteAbortedException;->setDefaultImpl()V

    return-void
.end method

.method private final getDefaultImpl(Landroid/view/View;Lcom/abox/apps/model/VoiceInfo;I)V
    .locals 6

    invoke-virtual {p2}, Lcom/abox/apps/model/VoiceInfo;->getCover()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-eqz v0, :cond_0

    sget v0, Lo/Cursor$LoaderManager;->onConnectionSuspended:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/ImageView;

    invoke-virtual {p2}, Lcom/abox/apps/model/VoiceInfo;->getCover()Ljava/lang/Object;

    move-result-object v1

    const-string v2, "null cannot be cast to non-null type kotlin.Int"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageResource(I)V

    :cond_0
    sget v0, Lo/Cursor$LoaderManager;->getResultContract:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    const-string v1, "findViewById(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p2}, Lcom/abox/apps/model/VoiceInfo;->getVip()Ljava/lang/Boolean;

    move-result-object v1

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    move v1, v2

    goto :goto_0

    :cond_1
    const/16 v1, 0x8

    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    sget v0, Lo/Cursor$LoaderManager;->ComponentActivity$ReportFullyDrawnExecutorApi16Impl$$ExternalSyntheticLambda0:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    sget v1, Lo/Cursor$TaskStackBuilder;->PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1:I

    const/4 v3, 0x1

    new-array v4, v3, [Ljava/lang/Object;

    invoke-virtual {p2}, Lcom/abox/apps/model/VoiceInfo;->getName()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v4, v2

    invoke-static {v1, v4}, Lo/aln;->asBinder(I[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    sget v0, Lo/Cursor$LoaderManager;->removeCancellable:I

    invoke-virtual {p1, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    sget v1, Lo/Cursor$LoaderManager;->isFullyDrawnReported:I

    invoke-virtual {p1, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/widget/TextView;

    if-ne v3, p3, :cond_2

    sget v1, Lo/Cursor$TaskStackBuilder;->ComponentActivity$$ExternalSyntheticLambda1:I

    goto :goto_1

    :cond_2
    sget v1, Lo/Cursor$TaskStackBuilder;->PipHintTrackerKt$trackPipAnimationHintView$flow$1$$ExternalSyntheticLambda1:I

    :goto_1
    invoke-static {v1}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    sget v1, Lo/Cursor$TaskStackBuilder;->ActivityViewModelLazyKt$viewModels$3:I

    invoke-static {v1}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    new-instance v1, Lo/ArrayStoreException;

    invoke-direct {v1, p3, p2, p0}, Lo/ArrayStoreException;-><init>(ILcom/abox/apps/model/VoiceInfo;Lo/WriteAbortedException;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    new-instance p2, Lo/Boolean;

    invoke-direct {p2, p0}, Lo/Boolean;-><init>(Lo/WriteAbortedException;)V

    invoke-virtual {p1, p2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public static synthetic getDefaultImpl(ZLo/WriteAbortedException;Lcom/abox/apps/model/VoiceInfo;ILandroid/view/View;)V
    .locals 0

    invoke-static {p0, p1, p2, p3, p4}, Lo/WriteAbortedException;->setDefaultImpl(ZLo/WriteAbortedException;Lcom/abox/apps/model/VoiceInfo;ILandroid/view/View;)V

    return-void
.end method

.method private static final onTransact(ILcom/abox/apps/model/VoiceInfo;Lo/WriteAbortedException;Landroid/view/View;)V
    .locals 7

    const-string p3, "$voiceInfo"

    invoke-static {p1, p3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string/jumbo p3, "this$0"

    invoke-static {p2, p3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 p3, 0x1

    if-ne p3, p0, :cond_0

    new-instance p0, Landroid/content/Intent;

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object p1

    const-class p3, Lcom/abox/apps/activitys/LoginGuideActivity;

    invoke-direct {p0, p1, p3}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object p1

    invoke-static {p1, p0}, Lo/akw;->getDefaultImpl(Landroid/content/Context;Landroid/content/Intent;)Z

    goto :goto_0

    :cond_0
    const/4 p3, 0x2

    if-ne p3, p0, :cond_1

    sget-object v0, Lcom/abox/apps/activitys/FreeTryActivity;->getDefaultImpl:Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object v1

    const-string p0, "getContext(...)"

    invoke-static {v1, p0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lcom/abox/apps/model/VoiceInfo;->getId()I

    move-result v4

    const/4 v2, 0x7

    const/4 v3, 0x0

    const/4 v5, 0x4

    const/4 v6, 0x0

    invoke-static/range {v0 .. v6}, Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;->asInterface(Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;Landroid/content/Context;ILjava/lang/String;IILjava/lang/Object;)V

    :cond_1
    :goto_0
    invoke-virtual {p2}, Lo/WriteAbortedException;->setDefaultImpl()V

    sget-object p0, Lo/FileNotFoundException;->asBinder:Lo/FileNotFoundException;

    invoke-virtual {p0}, Lo/FileNotFoundException;->getDefaultImpl()V

    return-void
.end method

.method private final onTransact(Lcom/abox/apps/model/VoiceInfo;I)V
    .locals 13

    sget-object v0, Lo/FileNotFoundException;->asBinder:Lo/FileNotFoundException;

    invoke-virtual {v0}, Lo/FileNotFoundException;->onTransact()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-ne v1, v3, :cond_0

    move v1, v3

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    sget-object v4, Lo/InputEventReceiver;->setDefaultImpl:Lo/InputEventReceiver$TaskDescription;

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object v5

    const-string v6, "getContext(...)"

    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v4, v5}, Lo/InputEventReceiver$TaskDescription;->onTransact(Landroid/content/Context;)Lo/InputEventReceiver$Activity;

    move-result-object v4

    sget v5, Lo/Cursor$Fragment;->$r8$lambda$hrfbBpVkizzQbtGmrw6DTx0hH1A:I

    new-instance v6, Lo/Writer;

    invoke-direct {v6, v1, p0, p1, p2}, Lo/Writer;-><init>(ZLo/WriteAbortedException;Lcom/abox/apps/model/VoiceInfo;I)V

    invoke-virtual {v4, v5, v6}, Lo/InputEventReceiver$Activity;->asInterface(ILo/SubMenu;)Lo/InputEventReceiver$Activity;

    move-result-object v7

    const/16 v8, 0x11

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x6

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lo/InputEventReceiver$Activity;->getDefaultImpl(Lo/InputEventReceiver$Activity;IIIILjava/lang/Object;)Lo/InputEventReceiver$Activity;

    move-result-object p1

    sget-object v1, Lo/RenderNodeAnimator;->ALL_TIME:Lo/RenderNodeAnimator;

    invoke-virtual {p1, v1}, Lo/InputEventReceiver$Activity;->onTransact(Lo/RenderNodeAnimator;)Lo/InputEventReceiver$Activity;

    move-result-object p1

    if-ne v3, p2, :cond_1

    const-string p2, "LOGIN_TAG"

    goto :goto_1

    :cond_1
    const-string p2, "UPGRADE_TAG"

    :goto_1
    invoke-virtual {p1, p2}, Lo/InputEventReceiver$Activity;->getDefaultImpl(Ljava/lang/String;)Lo/InputEventReceiver$Activity;

    move-result-object p1

    invoke-virtual {p1, v2}, Lo/InputEventReceiver$Activity;->getDefaultImpl(Z)Lo/InputEventReceiver$Activity;

    move-result-object p1

    invoke-virtual {v0}, Lo/FileNotFoundException;->onTransact()I

    move-result p2

    if-eq p2, v3, :cond_2

    const/16 p2, 0x64

    invoke-virtual {p1, p2, p2}, Lo/InputEventReceiver$Activity;->setDefaultImpl(II)Lo/InputEventReceiver$Activity;

    :cond_2
    invoke-virtual {p1}, Lo/InputEventReceiver$Activity;->onTransact()V

    return-void
.end method

.method public static synthetic setDefaultImpl(ILcom/abox/apps/model/VoiceInfo;Lo/WriteAbortedException;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lo/WriteAbortedException;->onTransact(ILcom/abox/apps/model/VoiceInfo;Lo/WriteAbortedException;Landroid/view/View;)V

    return-void
.end method

.method private final setDefaultImpl(Lcom/abox/apps/model/VoiceInfo;I)V
    .locals 2

    invoke-static {}, Lo/akx;->getDefaultImpl()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0}, Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;->setDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

    move-result-object v0

    const-string v1, "inflate(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lo/WriteAbortedException;->onTransact(Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;)V

    invoke-virtual {p0}, Lo/WriteAbortedException;->asBinder()Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

    move-result-object v0

    invoke-virtual {v0}, Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;->setDefaultImpl()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v1, "getRoot(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, v0, p1, p2}, Lo/WriteAbortedException;->getDefaultImpl(Landroid/view/View;Lcom/abox/apps/model/VoiceInfo;I)V

    return-void
.end method

.method private static final setDefaultImpl(ZLo/WriteAbortedException;Lcom/abox/apps/model/VoiceInfo;ILandroid/view/View;)V
    .locals 1

    const-string/jumbo v0, "this$0"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "$voiceInfo"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p4}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    if-eqz p0, :cond_0

    iget p0, p1, Lo/WriteAbortedException;->write:I

    goto :goto_0

    :cond_0
    iget p0, p1, Lo/WriteAbortedException;->write:I

    div-int/lit8 p0, p0, 0x2

    :goto_0
    iput p0, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    invoke-static {p4}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {p1, p4, p2, p3}, Lo/WriteAbortedException;->getDefaultImpl(Landroid/view/View;Lcom/abox/apps/model/VoiceInfo;I)V

    return-void
.end method


# virtual methods
.method public final asBinder()Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;
    .locals 1
    .annotation build Lo/cbz;
    .end annotation

    iget-object v0, p0, Lo/WriteAbortedException;->read:Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    const-string v0, "binding"

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    const/4 v0, 0x0

    return-object v0
.end method

.method public final asBinder(Landroid/content/Context;Lcom/abox/apps/model/VoiceInfo;IZ)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .param p2    # Lcom/abox/apps/model/VoiceInfo;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    # Disable premium upgrade dialog - do nothing
    return-void
.end method

.method public final asInterface()I
    .locals 1

    iget v0, p0, Lo/WriteAbortedException;->RemoteActionCompatParcelizer:I

    return v0
.end method

.method public final getDefaultImpl()Landroid/app/Dialog;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lo/WriteAbortedException;->IconCompatParcelizer:Landroid/app/Dialog;

    return-object v0
.end method

.method public final onTransact(Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;)V
    .locals 1
    .param p1    # Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    iput-object p1, p0, Lo/WriteAbortedException;->read:Lcom/abox/apps/databinding/ViewLoginAndPremiumBinding;

    return-void
.end method

.method public final setDefaultImpl()V
    .locals 5

    iget-object v0, p0, Lo/WriteAbortedException;->IconCompatParcelizer:Landroid/app/Dialog;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/app/Dialog;->dismiss()V

    :cond_0
    sget-object v0, Lo/InputEventReceiver;->setDefaultImpl:Lo/InputEventReceiver$TaskDescription;

    const/4 v1, 0x0

    const/4 v2, 0x2

    const/4 v3, 0x0

    const-string v4, "LOGIN_TAG"

    invoke-static {v0, v4, v3, v2, v1}, Lo/InputEventReceiver$TaskDescription;->asInterface(Lo/InputEventReceiver$TaskDescription;Ljava/lang/String;ZILjava/lang/Object;)Lkotlin/Unit;

    const-string v4, "UPGRADE_TAG"

    invoke-static {v0, v4, v3, v2, v1}, Lo/InputEventReceiver$TaskDescription;->asInterface(Lo/InputEventReceiver$TaskDescription;Ljava/lang/String;ZILjava/lang/Object;)Lkotlin/Unit;

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    new-instance v1, Landroid/os/Bundle;

    invoke-direct {v1}, Landroid/os/Bundle;-><init>()V

    iget v2, p0, Lo/WriteAbortedException;->onConnected:I

    const-string/jumbo v3, "voiceId"

    invoke-virtual {v1, v3, v2}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    sget-object v2, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    const-string v2, "nmPopupClose"

    invoke-virtual {v0, v2, v1}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;Landroid/os/Bundle;)V

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    invoke-virtual {v0, v2}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    return-void
.end method

.method public final setDefaultImpl(I)V
    .locals 0

    iput p1, p0, Lo/WriteAbortedException;->RemoteActionCompatParcelizer:I

    return-void
.end method

.method public final setDefaultImpl(Landroid/app/Dialog;)V
    .locals 0
    .param p1    # Landroid/app/Dialog;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lo/WriteAbortedException;->IconCompatParcelizer:Landroid/app/Dialog;

    return-void
.end method
