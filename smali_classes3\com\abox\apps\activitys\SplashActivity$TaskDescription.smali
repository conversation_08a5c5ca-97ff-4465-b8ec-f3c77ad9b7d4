.class final Lcom/abox/apps/activitys/SplashActivity$TaskDescription;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/abox/apps/activitys/SplashActivity;->onTransact()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/CoroutineScope;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"
    }
    d2 = {
        "<anonymous>",
        "",
        "Lkotlinx/coroutines/CoroutineScope;"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.abox.apps.activitys.SplashActivity$onAfterViews$1"
    f = "SplashActivity.kt"
    i = {}
    l = {}
    m = "invokeSuspend"
    n = {}
    s = {}
.end annotation


# instance fields
.field final synthetic asInterface:Lcom/abox/apps/activitys/SplashActivity;

.field onTransact:I


# direct methods
.method constructor <init>(Lcom/abox/apps/activitys/SplashActivity;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/abox/apps/activitys/SplashActivity;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/abox/apps/activitys/SplashActivity$TaskDescription;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    new-instance p1, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;

    iget-object v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    invoke-direct {p1, v0, p2}, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;-><init>(Lcom/abox/apps/activitys/SplashActivity;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/CoroutineScope;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->setDefaultImpl(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbw;
    .end annotation

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->getCOROUTINE_SUSPENDED()Ljava/lang/Object;

    iget v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->onTransact:I

    if-nez v0, :cond_0

    invoke-static {p1}, Lkotlin/ResultKt;->throwOnFailure(Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    invoke-static {v0}, Lcom/abox/apps/activitys/UnlockActivity;->isActivated(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_1

    sget-object p1, Lcom/abox/apps/activitys/AcquirePermissionGuideActivity;->asInterface:Lcom/abox/apps/activitys/AcquirePermissionGuideActivity$Application;

    iget-object v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    invoke-virtual {p1, v0}, Lcom/abox/apps/activitys/AcquirePermissionGuideActivity$Application;->asBinder(Landroid/content/Context;)V

    goto :goto_1

    :cond_1
    new-instance p1, Landroid/content/Intent;

    iget-object v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    const-class v1, Lcom/abox/apps/activitys/UnlockActivity;

    invoke-direct {p1, v0, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    iget-object v0, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    invoke-virtual {v0, p1}, Landroid/app/Activity;->startActivity(Landroid/content/Intent;)V

    :goto_1
    iget-object p1, p0, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->asInterface:Lcom/abox/apps/activitys/SplashActivity;

    invoke-virtual {p1}, Lcom/abox/apps/activitys/SplashActivity;->finish()V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final setDefaultImpl(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlinx/coroutines/CoroutineScope;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/CoroutineScope;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;

    sget-object p2, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/abox/apps/activitys/SplashActivity$TaskDescription;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
