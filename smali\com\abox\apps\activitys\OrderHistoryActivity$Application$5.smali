.class final Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/CoroutineScope;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"
    }
    d2 = {
        "<anonymous>",
        "",
        "Lkotlinx/coroutines/CoroutineScope;"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.abox.apps.activitys.OrderHistoryActivity$onAfterViews$4$1"
    f = "OrderHistoryActivity.kt"
    i = {}
    l = {}
    m = "invokeSuspend"
    n = {}
    s = {}
.end annotation


# instance fields
.field final synthetic asInterface:Lcom/abox/apps/model/BaseResponse;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/abox/apps/model/BaseResponse<",
            "Ljava/util/ArrayList<",
            "Lcom/abox/apps/model/OrderHistoryResponse;",
            ">;>;"
        }
    .end annotation
.end field

.field final synthetic getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

.field setDefaultImpl:I


# direct methods
.method constructor <init>(Lcom/abox/apps/model/BaseResponse;Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/abox/apps/model/BaseResponse<",
            "Ljava/util/ArrayList<",
            "Lcom/abox/apps/model/OrderHistoryResponse;",
            ">;>;",
            "Lcom/abox/apps/activitys/OrderHistoryActivity;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface:Lcom/abox/apps/model/BaseResponse;

    iput-object p2, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final asInterface(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlinx/coroutines/CoroutineScope;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/CoroutineScope;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;

    sget-object p2, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 2
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    new-instance p1, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;

    iget-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface:Lcom/abox/apps/model/BaseResponse;

    iget-object v1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-direct {p1, v0, v1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;-><init>(Lcom/abox/apps/model/BaseResponse;Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/CoroutineScope;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbw;
    .end annotation

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->getCOROUTINE_SUSPENDED()Ljava/lang/Object;

    iget v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->setDefaultImpl:I

    if-nez v0, :cond_6

    invoke-static {p1}, Lkotlin/ResultKt;->throwOnFailure(Ljava/lang/Object;)V

    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface:Lcom/abox/apps/model/BaseResponse;

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/abox/apps/model/BaseResponse;->isSucceed()Z

    move-result p1

    if-ne p1, v1, :cond_0

    move p1, v1

    goto :goto_0

    :cond_0
    move p1, v0

    :goto_0
    const-string v2, "historyListAdapter"

    const/4 v3, 0x0

    if-eqz p1, :cond_3

    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface:Lcom/abox/apps/model/BaseResponse;

    invoke-virtual {p1}, Lcom/abox/apps/model/BaseResponse;->getData()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/ArrayList;

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_1

    move v0, v1

    :cond_1
    if-eqz v0, :cond_3

    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-static {p1}, Lcom/abox/apps/activitys/OrderHistoryActivity;->setDefaultImpl(Lcom/abox/apps/activitys/OrderHistoryActivity;)Lcom/abox/apps/adapters/HistoryListAdapter;

    move-result-object p1

    if-nez p1, :cond_2

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object p1, v3

    :cond_2
    iget-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->asInterface:Lcom/abox/apps/model/BaseResponse;

    invoke-virtual {v0}, Lcom/abox/apps/model/BaseResponse;->getData()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    invoke-virtual {p1, v0}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->asInterface(Ljava/util/List;)V

    goto :goto_1

    :cond_3
    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-static {p1}, Lcom/abox/apps/activitys/OrderHistoryActivity;->setDefaultImpl(Lcom/abox/apps/activitys/OrderHistoryActivity;)Lcom/abox/apps/adapters/HistoryListAdapter;

    move-result-object p1

    if-nez p1, :cond_4

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object p1, v3

    :cond_4
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-virtual {p1, v0}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->asInterface(Ljava/util/List;)V

    :goto_1
    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-static {p1}, Lcom/abox/apps/activitys/OrderHistoryActivity;->asInterface(Lcom/abox/apps/activitys/OrderHistoryActivity;)Lo/StorageEventListener;

    move-result-object p1

    if-eqz p1, :cond_5

    invoke-virtual {p1}, Lo/StorageEventListener;->setDefaultImpl()V

    :cond_5
    iget-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;->getDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-static {p1, v3}, Lcom/abox/apps/activitys/OrderHistoryActivity;->asBinder(Lcom/abox/apps/activitys/OrderHistoryActivity;Lo/StorageEventListener;)V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1

    :cond_6
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
