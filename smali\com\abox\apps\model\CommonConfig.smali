.class public final Lcom/abox/apps/model/CommonConfig;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/model/CommonConfig$Companion;,
        Lcom/abox/apps/model/CommonConfig$TextBean;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u000e\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0010 \n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0015\u0018\u0000 >2\u00020\u0001:\u0002>?B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0008\u0010=\u001a\u0004\u0018\u00010\u0004R \u0010\u0003\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0005\u0010\u0006\"\u0004\u0008\u0007\u0010\u0008R \u0010\t\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\n\u0010\u0006\"\u0004\u0008\u000b\u0010\u0008R \u0010\u000c\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\r\u0010\u0006\"\u0004\u0008\u000e\u0010\u0008R \u0010\u000f\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0010\u0010\u0006\"\u0004\u0008\u0011\u0010\u0008R\u001a\u0010\u0012\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0014\u0010\u0015\"\u0004\u0008\u0016\u0010\u0017R \u0010\u0018\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0019\u0010\u0006\"\u0004\u0008\u001a\u0010\u0008R\u001e\u0010\u001b\u001a\u00020\u001c8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u001d\u0010\u001e\"\u0004\u0008\u001f\u0010 R\u001e\u0010!\u001a\u00020\u001c8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\"\u0010\u001e\"\u0004\u0008#\u0010 R&\u0010$\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010%8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008&\u0010\'\"\u0004\u0008(\u0010)R&\u0010*\u001a\n\u0012\u0004\u0012\u00020+\u0018\u00010%8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008,\u0010\'\"\u0004\u0008-\u0010)R \u0010.\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008/\u0010\u0006\"\u0004\u00080\u0010\u0008R \u00101\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00082\u0010\u0006\"\u0004\u00083\u0010\u0008R \u00104\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00085\u0010\u0006\"\u0004\u00086\u0010\u0008R \u00107\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00088\u0010\u0006\"\u0004\u00089\u0010\u0008R \u0010:\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008;\u0010\u0006\"\u0004\u0008<\u0010\u0008\u00a8\u0006@"
    }
    d2 = {
        "Lcom/abox/apps/model/CommonConfig;",
        "",
        "()V",
        "currentPrice",
        "",
        "getCurrentPrice",
        "()Ljava/lang/String;",
        "setCurrentPrice",
        "(Ljava/lang/String;)V",
        "customProxyApp",
        "getCustomProxyApp",
        "setCustomProxyApp",
        "discordGpJumpUrl",
        "getDiscordGpJumpUrl",
        "setDiscordGpJumpUrl",
        "discordJumpUrl",
        "getDiscordJumpUrl",
        "setDiscordJumpUrl",
        "origin",
        "",
        "getOrigin",
        "()I",
        "setOrigin",
        "(I)V",
        "originalPrice",
        "getOriginalPrice",
        "setOriginalPrice",
        "refreshInterval",
        "",
        "getRefreshInterval",
        "()J",
        "setRefreshInterval",
        "(J)V",
        "refreshTimestamp",
        "getRefreshTimestamp",
        "setRefreshTimestamp",
        "requireGoogleLogin",
        "",
        "getRequireGoogleLogin",
        "()Ljava/util/List;",
        "setRequireGoogleLogin",
        "(Ljava/util/List;)V",
        "scrollingSubtitles",
        "Lcom/abox/apps/model/CommonConfig$TextBean;",
        "getScrollingSubtitles",
        "setScrollingSubtitles",
        "telegramGpJumpUrl",
        "getTelegramGpJumpUrl",
        "setTelegramGpJumpUrl",
        "telegramJumpUrl",
        "getTelegramJumpUrl",
        "setTelegramJumpUrl",
        "trialShopPageJson",
        "getTrialShopPageJson",
        "setTrialShopPageJson",
        "youtubeGpJumpUrl",
        "getYoutubeGpJumpUrl",
        "setYoutubeGpJumpUrl",
        "youtubeJumpUrl",
        "getYoutubeJumpUrl",
        "setYoutubeJumpUrl",
        "getScrollingSubtitle",
        "Companion",
        "TextBean",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nCommonConfig.kt\nKotlin\n*S Kotlin\n*F\n+ 1 CommonConfig.kt\ncom/abox/apps/model/CommonConfig\n+ 2 fake.kt\nkotlin/jvm/internal/FakeKt\n*L\n1#1,106:1\n1#2:107\n*E\n"
    }
.end annotation


# static fields
.field public static final CONFIG_REFRESH_INTERVAL:Ljava/lang/String; = "config_refresh_interval"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final CURRENT_PRICE:Ljava/lang/String; = "current_price"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final CUSTOM_PROXY_APP:Ljava/lang/String; = "customProxyApp"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final Companion:Lcom/abox/apps/model/CommonConfig$Companion;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final DEFAULT_REFRESH_PERIOD:J = 0x3cL

.field public static final DISCORD_GP_JUMP_URL:Ljava/lang/String; = "discord_gpjump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final DISCORD_JUMP_URL:Ljava/lang/String; = "discord_jump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final ORIGINAL_PRICE:Ljava/lang/String; = "original_price"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final ORIGIN_FROM_CACHE:I = 0x0

.field public static final ORIGIN_FROM_DEFAULT:I = 0x2

.field public static final ORIGIN_FROM_SERVER:I = 0x1

.field public static final REFRESH_TIMESTAMP:Ljava/lang/String; = "refreshTimestamp"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final REQUIRE_GOOGLE_LOGIN:Ljava/lang/String; = "require_google_login"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final SCROLLING_SUBTITLES:Ljava/lang/String; = "scrolling_subtitles"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final TELEGRAM_GP_JUMP_URL:Ljava/lang/String; = "telegram_gpjump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final TELEGRAM_JUMP_URL:Ljava/lang/String; = "telegram_jump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final TRIAL_SHOP_PAGE:Ljava/lang/String; = "trial_shop_page"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final YOUTUBE_GP_JUMP_URL:Ljava/lang/String; = "youtube_gpjump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final YOUTUBE_JUMP_URL:Ljava/lang/String; = "youtube_jump_url"
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field private currentPrice:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "current_price"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private customProxyApp:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "customProxyApp"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private discordGpJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "discord_gpjump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private discordJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "discord_jump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private origin:I

.field private originalPrice:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "original_price"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private refreshInterval:J
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "config_refresh_interval"
    .end annotation
.end field

.field private refreshTimestamp:J
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "refreshTimestamp"
    .end annotation
.end field

.field private requireGoogleLogin:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "require_google_login"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private scrollingSubtitles:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "scrolling_subtitles"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/abox/apps/model/CommonConfig$TextBean;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private telegramGpJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "telegram_gpjump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private telegramJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "telegram_jump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private trialShopPageJson:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "trial_shop_page"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private youtubeGpJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "youtube_gpjump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field

.field private youtubeJumpUrl:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "youtube_jump_url"
    .end annotation

    .annotation build Lo/cbw;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/model/CommonConfig$Companion;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/model/CommonConfig$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/model/CommonConfig;->Companion:Lcom/abox/apps/model/CommonConfig$Companion;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x3c

    iput-wide v0, p0, Lcom/abox/apps/model/CommonConfig;->refreshInterval:J

    return-void
.end method


# virtual methods
.method public final getCurrentPrice()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->currentPrice:Ljava/lang/String;

    return-object v0
.end method

.method public final getCustomProxyApp()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->customProxyApp:Ljava/lang/String;

    return-object v0
.end method

.method public final getDiscordGpJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->discordGpJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getDiscordJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->discordJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getOrigin()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/CommonConfig;->origin:I

    return v0
.end method

.method public final getOriginalPrice()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->originalPrice:Ljava/lang/String;

    return-object v0
.end method

.method public final getRefreshInterval()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/CommonConfig;->refreshInterval:J

    return-wide v0
.end method

.method public final getRefreshTimestamp()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/CommonConfig;->refreshTimestamp:J

    return-wide v0
.end method

.method public final getRequireGoogleLogin()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation

    # Always return null to bypass login requirements
    const/4 v0, 0x0

    return-object v0
.end method

.method public final getScrollingSubtitle()Ljava/lang/String;
    .locals 6
    .annotation build Lo/cbw;
    .end annotation

    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/Locale;->getLanguage()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/abox/apps/model/CommonConfig;->scrollingSubtitles:Ljava/util/List;

    const/4 v2, 0x0

    if-eqz v1, :cond_9

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_4

    :cond_0
    iget-object v1, p0, Lcom/abox/apps/model/CommonConfig;->scrollingSubtitles:Ljava/util/List;

    if-eqz v1, :cond_3

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Lcom/abox/apps/model/CommonConfig$TextBean;

    invoke-virtual {v4}, Lcom/abox/apps/model/CommonConfig$TextBean;->getDictValue()Ljava/lang/String;

    move-result-object v4

    invoke-static {v0, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    goto :goto_0

    :cond_2
    move-object v3, v2

    :goto_0
    check-cast v3, Lcom/abox/apps/model/CommonConfig$TextBean;

    goto :goto_1

    :cond_3
    move-object v3, v2

    :goto_1
    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->scrollingSubtitles:Ljava/util/List;

    if-eqz v0, :cond_6

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, Lcom/abox/apps/model/CommonConfig$TextBean;

    invoke-virtual {v4}, Lcom/abox/apps/model/CommonConfig$TextBean;->getDictValue()Ljava/lang/String;

    move-result-object v4

    const-string v5, "en"

    invoke-static {v5, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_2

    :cond_5
    move-object v1, v2

    :goto_2
    check-cast v1, Lcom/abox/apps/model/CommonConfig$TextBean;

    goto :goto_3

    :cond_6
    move-object v1, v2

    :goto_3
    if-nez v3, :cond_8

    if-eqz v1, :cond_7

    invoke-virtual {v1}, Lcom/abox/apps/model/CommonConfig$TextBean;->getRemark()Ljava/lang/String;

    move-result-object v2

    :cond_7
    return-object v2

    :cond_8
    invoke-virtual {v3}, Lcom/abox/apps/model/CommonConfig$TextBean;->getRemark()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_9
    :goto_4
    return-object v2
.end method

.method public final getScrollingSubtitles()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/abox/apps/model/CommonConfig$TextBean;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->scrollingSubtitles:Ljava/util/List;

    return-object v0
.end method

.method public final getTelegramGpJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->telegramGpJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getTelegramJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->telegramJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getTrialShopPageJson()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->trialShopPageJson:Ljava/lang/String;

    return-object v0
.end method

.method public final getYoutubeGpJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->youtubeGpJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final getYoutubeJumpUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/CommonConfig;->youtubeJumpUrl:Ljava/lang/String;

    return-object v0
.end method

.method public final setCurrentPrice(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->currentPrice:Ljava/lang/String;

    return-void
.end method

.method public final setCustomProxyApp(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->customProxyApp:Ljava/lang/String;

    return-void
.end method

.method public final setDiscordGpJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->discordGpJumpUrl:Ljava/lang/String;

    return-void
.end method

.method public final setDiscordJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->discordJumpUrl:Ljava/lang/String;

    return-void
.end method

.method public final setOrigin(I)V
    .locals 0

    iput p1, p0, Lcom/abox/apps/model/CommonConfig;->origin:I

    return-void
.end method

.method public final setOriginalPrice(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->originalPrice:Ljava/lang/String;

    return-void
.end method

.method public final setRefreshInterval(J)V
    .locals 0

    iput-wide p1, p0, Lcom/abox/apps/model/CommonConfig;->refreshInterval:J

    return-void
.end method

.method public final setRefreshTimestamp(J)V
    .locals 0

    iput-wide p1, p0, Lcom/abox/apps/model/CommonConfig;->refreshTimestamp:J

    return-void
.end method

.method public final setRequireGoogleLogin(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->requireGoogleLogin:Ljava/util/List;

    return-void
.end method

.method public final setScrollingSubtitles(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/abox/apps/model/CommonConfig$TextBean;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->scrollingSubtitles:Ljava/util/List;

    return-void
.end method

.method public final setTelegramGpJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->telegramGpJumpUrl:Ljava/lang/String;

    return-void
.end method

.method public final setTelegramJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->telegramJumpUrl:Ljava/lang/String;

    return-void
.end method

.method public final setTrialShopPageJson(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->trialShopPageJson:Ljava/lang/String;

    return-void
.end method

.method public final setYoutubeGpJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->youtubeGpJumpUrl:Ljava/lang/String;

    return-void
.end method

.method public final setYoutubeJumpUrl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/CommonConfig;->youtubeJumpUrl:Ljava/lang/String;

    return-void
.end method
