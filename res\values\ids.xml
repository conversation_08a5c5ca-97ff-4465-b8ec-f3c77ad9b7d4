<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="BaseQuickAdapter_databinding_support" />
    <item type="id" name="BaseQuickAdapter_dragging_support" />
    <item type="id" name="BaseQuickAdapter_swiping_support" />
    <item type="id" name="BaseQuickAdapter_viewholder_support" />
    <item type="id" name="IS_VIEW_FADING_TAG" />
    <item type="id" name="_ll_temp" />
    <item type="id" name="about_us" />
    <item type="id" name="accessibility_action_clickable_span" />
    <item type="id" name="accessibility_custom_action_0" />
    <item type="id" name="accessibility_custom_action_1" />
    <item type="id" name="accessibility_custom_action_10" />
    <item type="id" name="accessibility_custom_action_11" />
    <item type="id" name="accessibility_custom_action_12" />
    <item type="id" name="accessibility_custom_action_13" />
    <item type="id" name="accessibility_custom_action_14" />
    <item type="id" name="accessibility_custom_action_15" />
    <item type="id" name="accessibility_custom_action_16" />
    <item type="id" name="accessibility_custom_action_17" />
    <item type="id" name="accessibility_custom_action_18" />
    <item type="id" name="accessibility_custom_action_19" />
    <item type="id" name="accessibility_custom_action_2" />
    <item type="id" name="accessibility_custom_action_20" />
    <item type="id" name="accessibility_custom_action_21" />
    <item type="id" name="accessibility_custom_action_22" />
    <item type="id" name="accessibility_custom_action_23" />
    <item type="id" name="accessibility_custom_action_24" />
    <item type="id" name="accessibility_custom_action_25" />
    <item type="id" name="accessibility_custom_action_26" />
    <item type="id" name="accessibility_custom_action_27" />
    <item type="id" name="accessibility_custom_action_28" />
    <item type="id" name="accessibility_custom_action_29" />
    <item type="id" name="accessibility_custom_action_3" />
    <item type="id" name="accessibility_custom_action_30" />
    <item type="id" name="accessibility_custom_action_31" />
    <item type="id" name="accessibility_custom_action_4" />
    <item type="id" name="accessibility_custom_action_5" />
    <item type="id" name="accessibility_custom_action_6" />
    <item type="id" name="accessibility_custom_action_7" />
    <item type="id" name="accessibility_custom_action_8" />
    <item type="id" name="accessibility_custom_action_9" />
    <item type="id" name="account_row_icon" />
    <item type="id" name="account_row_text" />
    <item type="id" name="action0" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_share" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="ad_close_icon" />
    <item type="id" name="ad_control_button" />
    <item type="id" name="ad_controls_view" />
    <item type="id" name="ad_presenter_view" />
    <item type="id" name="ad_view_container" />
    <item type="id" name="addManuallyImage" />
    <item type="id" name="addManuallyParentView" />
    <item type="id" name="addManuallyText" />
    <item type="id" name="add_feedback_et" />
    <item type="id" name="add_new_address_button" />
    <item type="id" name="add_new_card" />
    <item type="id" name="addressBookBackArrowImageView" />
    <item type="id" name="addressBookHeaderLayout" />
    <item type="id" name="addressBookParentView" />
    <item type="id" name="addressBookRecyclerView" />
    <item type="id" name="addressBookTitleTextView" />
    <item type="id" name="addressLayout" />
    <item type="id" name="addressLine1" />
    <item type="id" name="addressLine2" />
    <item type="id" name="addressLine2EditText" />
    <item type="id" name="addressReviewEditText" />
    <item type="id" name="address_book_city_state_zip_tv" />
    <item type="id" name="address_book_current_location_tv" />
    <item type="id" name="address_book_first_line_tv" />
    <item type="id" name="address_book_header_container" />
    <item type="id" name="address_book_invalid_tv" />
    <item type="id" name="address_book_left_tab_tv" />
    <item type="id" name="address_book_left_tab_view" />
    <item type="id" name="address_book_parent_view" />
    <item type="id" name="address_book_right_tab_tv" />
    <item type="id" name="address_book_right_tab_view" />
    <item type="id" name="address_book_second_line_tv" />
    <item type="id" name="address_book_ship_to_tv" />
    <item type="id" name="address_book_underline_container" />
    <item type="id" name="al_exo_ad_overlay" />
    <item type="id" name="al_exo_artwork" />
    <item type="id" name="al_exo_audio_track" />
    <item type="id" name="al_exo_basic_controls" />
    <item type="id" name="al_exo_bottom_bar" />
    <item type="id" name="al_exo_buffering" />
    <item type="id" name="al_exo_center_controls" />
    <item type="id" name="al_exo_content_frame" />
    <item type="id" name="al_exo_controller" />
    <item type="id" name="al_exo_controller_placeholder" />
    <item type="id" name="al_exo_controls_background" />
    <item type="id" name="al_exo_duration" />
    <item type="id" name="al_exo_error_message" />
    <item type="id" name="al_exo_extra_controls" />
    <item type="id" name="al_exo_extra_controls_scroll_view" />
    <item type="id" name="al_exo_ffwd" />
    <item type="id" name="al_exo_ffwd_with_amount" />
    <item type="id" name="al_exo_fullscreen" />
    <item type="id" name="al_exo_minimal_controls" />
    <item type="id" name="al_exo_minimal_fullscreen" />
    <item type="id" name="al_exo_next" />
    <item type="id" name="al_exo_overflow_hide" />
    <item type="id" name="al_exo_overflow_show" />
    <item type="id" name="al_exo_overlay" />
    <item type="id" name="al_exo_pause" />
    <item type="id" name="al_exo_play" />
    <item type="id" name="al_exo_play_pause" />
    <item type="id" name="al_exo_playback_speed" />
    <item type="id" name="al_exo_position" />
    <item type="id" name="al_exo_prev" />
    <item type="id" name="al_exo_progress" />
    <item type="id" name="al_exo_progress_placeholder" />
    <item type="id" name="al_exo_repeat_toggle" />
    <item type="id" name="al_exo_rew" />
    <item type="id" name="al_exo_rew_with_amount" />
    <item type="id" name="al_exo_settings" />
    <item type="id" name="al_exo_shuffle" />
    <item type="id" name="al_exo_shutter" />
    <item type="id" name="al_exo_subtitle" />
    <item type="id" name="al_exo_subtitles" />
    <item type="id" name="al_exo_time" />
    <item type="id" name="al_exo_vr" />
    <item type="id" name="alertTitle" />
    <item type="id" name="alert_body" />
    <item type="id" name="alert_header" />
    <item type="id" name="alert_icon" />
    <item type="id" name="amp_eeInfo_btn_copyDeviceId" />
    <item type="id" name="amp_eeInfo_btn_copyUserId" />
    <item type="id" name="amp_eeInfo_iv_close" />
    <item type="id" name="amp_eeInfo_tv_deviceId" />
    <item type="id" name="amp_eeInfo_tv_userId" />
    <item type="id" name="analytics_purposes_switch" />
    <item type="id" name="analytics_purposes_switch_textview" />
    <item type="id" name="and_tv" />
    <item type="id" name="app_des" />
    <item type="id" name="app_list" />
    <item type="id" name="app_list_title" />
    <item type="id" name="app_name" />
    <item type="id" name="app_name_tv" />
    <item type="id" name="app_open_ad_control_button" />
    <item type="id" name="app_open_ad_control_view" />
    <item type="id" name="applovin_native_ad_badge_and_title_text_view" />
    <item type="id" name="applovin_native_ad_content_linear_layout" />
    <item type="id" name="applovin_native_ad_view_container" />
    <item type="id" name="applovin_native_advertiser_text_view" />
    <item type="id" name="applovin_native_badge_text_view" />
    <item type="id" name="applovin_native_body_text_view" />
    <item type="id" name="applovin_native_cta_button" />
    <item type="id" name="applovin_native_guideline" />
    <item type="id" name="applovin_native_icon_and_text_layout" />
    <item type="id" name="applovin_native_icon_image_view" />
    <item type="id" name="applovin_native_icon_view" />
    <item type="id" name="applovin_native_inner_linear_layout" />
    <item type="id" name="applovin_native_inner_parent_layout" />
    <item type="id" name="applovin_native_leader_icon_and_text_layout" />
    <item type="id" name="applovin_native_media_content_view" />
    <item type="id" name="applovin_native_options_view" />
    <item type="id" name="applovin_native_star_rating_view" />
    <item type="id" name="applovin_native_title_text_view" />
    <item type="id" name="apply_tv" />
    <item type="id" name="attachPopupContainer" />
    <item type="id" name="auth_fragment_host" />
    <item type="id" name="authorization_link" />
    <item type="id" name="autoCompleteTextView" />
    <item type="id" name="avatar" />
    <item type="id" name="avatar_iv" />
    <item type="id" name="ba_terms_text" />
    <item type="id" name="backArrowImageView" />
    <item type="id" name="backButtonWidthOffsetSpace" />
    <item type="id" name="back_arrow" />
    <item type="id" name="back_button" />
    <item type="id" name="back_iv" />
    <item type="id" name="background_view" />
    <item type="id" name="balance_amount_tv" />
    <item type="id" name="ball" />
    <item type="id" name="bank_name" />
    <item type="id" name="banner" />
    <item type="id" name="banner_ad_view_container" />
    <item type="id" name="banner_control_button" />
    <item type="id" name="banner_control_view" />
    <item type="id" name="banner_data_key" />
    <item type="id" name="banner_label" />
    <item type="id" name="banner_pos_key" />
    <item type="id" name="battery_optimization_enable" />
    <item type="id" name="battery_optimization_enable_title" />
    <item type="id" name="bg_id" />
    <item type="id" name="bg_positive" />
    <item type="id" name="bg_view" />
    <item type="id" name="body_container" />
    <item type="id" name="bottomPopupContainer" />
    <item type="id" name="bottomScrollView" />
    <item type="id" name="bottomSheetLayout" />
    <item type="id" name="bottom_break" />
    <item type="id" name="bottom_padding" />
    <item type="id" name="bottom_sheet_container" />
    <item type="id" name="browser_actions_header_text" />
    <item type="id" name="browser_actions_menu_item_icon" />
    <item type="id" name="browser_actions_menu_item_text" />
    <item type="id" name="browser_actions_menu_items" />
    <item type="id" name="browser_actions_menu_view" />
    <item type="id" name="btn_1" />
    <item type="id" name="btn_10" />
    <item type="id" name="btn_11" />
    <item type="id" name="btn_12" />
    <item type="id" name="btn_13" />
    <item type="id" name="btn_14" />
    <item type="id" name="btn_15" />
    <item type="id" name="btn_16" />
    <item type="id" name="btn_17" />
    <item type="id" name="btn_18" />
    <item type="id" name="btn_19" />
    <item type="id" name="btn_2" />
    <item type="id" name="btn_20" />
    <item type="id" name="btn_21" />
    <item type="id" name="btn_22" />
    <item type="id" name="btn_23" />
    <item type="id" name="btn_24" />
    <item type="id" name="btn_25" />
    <item type="id" name="btn_26" />
    <item type="id" name="btn_27" />
    <item type="id" name="btn_28" />
    <item type="id" name="btn_29" />
    <item type="id" name="btn_3" />
    <item type="id" name="btn_30" />
    <item type="id" name="btn_31" />
    <item type="id" name="btn_32" />
    <item type="id" name="btn_4" />
    <item type="id" name="btn_5" />
    <item type="id" name="btn_6" />
    <item type="id" name="btn_7" />
    <item type="id" name="btn_72hour_see_more" />
    <item type="id" name="btn_8" />
    <item type="id" name="btn_9" />
    <item type="id" name="btn_clone_app" />
    <item type="id" name="btn_conversion_options" />
    <item type="id" name="btn_text" />
    <item type="id" name="bubbleContainer" />
    <item type="id" name="buf_text" />
    <item type="id" name="button1" />
    <item type="id" name="button2" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="buttonText" />
    <item type="id" name="button_alternate_login" />
    <item type="id" name="button_save_address" />
    <item type="id" name="button_session_text" />
    <item type="id" name="button_space_wrapper" />
    <item type="id" name="button_view" />
    <item type="id" name="button_wrapper" />
    <item type="id" name="cancel" />
    <item type="id" name="cancel_action" />
    <item type="id" name="cancel_anytime" />
    <item type="id" name="cancel_btn" />
    <item type="id" name="cancel_button" />
    <item type="id" name="cardView_input_address_container" />
    <item type="id" name="cardView_rec_address_container" />
    <item type="id" name="cardView_source_container" />
    <item type="id" name="card_type_tv" />
    <item type="id" name="cart_details_layout" />
    <item type="id" name="cart_item_rv" />
    <item type="id" name="cart_list_parent_container" />
    <item type="id" name="cb_app_checked" />
    <item type="id" name="centerPopupContainer" />
    <item type="id" name="challengeInfoHeaderTextView" />
    <item type="id" name="challengeInfoLabelTextView" />
    <item type="id" name="challengeInfoTextView" />
    <item type="id" name="change_order" />
    <item type="id" name="change_voice_btn" />
    <item type="id" name="check_box" />
    <item type="id" name="check_view" />
    <item type="id" name="checkbox" />
    <item type="id" name="checked" />
    <item type="id" name="checked_iv" />
    <item type="id" name="checkout_button_parent_view" />
    <item type="id" name="checkout_button_view" />
    <item type="id" name="checkout_pb" />
    <item type="id" name="chip" />
    <item type="id" name="chip1" />
    <item type="id" name="chip2" />
    <item type="id" name="chip3" />
    <item type="id" name="chip_group" />
    <item type="id" name="chronometer" />
    <item type="id" name="circle_center" />
    <item type="id" name="cityEditText" />
    <item type="id" name="click_time" />
    <item type="id" name="close" />
    <item type="id" name="closeButton" />
    <item type="id" name="closeImageView" />
    <item type="id" name="close_icon" />
    <item type="id" name="codeEditTextField" />
    <item type="id" name="com_facebook_body_frame" />
    <item type="id" name="com_facebook_button_xout" />
    <item type="id" name="com_facebook_device_auth_instructions" />
    <item type="id" name="com_facebook_fragment_container" />
    <item type="id" name="com_facebook_login_fragment_progress_bar" />
    <item type="id" name="com_facebook_smart_instructions_0" />
    <item type="id" name="com_facebook_smart_instructions_or" />
    <item type="id" name="com_facebook_tooltip_bubble_view_bottom_pointer" />
    <item type="id" name="com_facebook_tooltip_bubble_view_text_body" />
    <item type="id" name="com_facebook_tooltip_bubble_view_top_pointer" />
    <item type="id" name="commonButtonLayout" />
    <item type="id" name="common_button_layout" />
    <item type="id" name="common_layout" />
    <item type="id" name="compound_header_parent_container" />
    <item type="id" name="confirm_button" />
    <item type="id" name="confirmation_code" />
    <item type="id" name="constraintLayout2" />
    <item type="id" name="constraintLayout3" />
    <item type="id" name="constraint_layout" />
    <item type="id" name="constraint_layout_card_info_container" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentFrameLayout" />
    <item type="id" name="contentPanel" />
    <item type="id" name="contentTextView" />
    <item type="id" name="content_container" />
    <item type="id" name="content_list" />
    <item type="id" name="content_scroll_view" />
    <item type="id" name="content_view" />
    <item type="id" name="continueButton" />
    <item type="id" name="continue_btn" />
    <item type="id" name="continue_button" />
    <item type="id" name="controls_view" />
    <item type="id" name="conversionBackArrowImageView" />
    <item type="id" name="conversionBottomSheetLayout" />
    <item type="id" name="conversionCircleImageView" />
    <item type="id" name="conversionCurrencyButton" />
    <item type="id" name="conversionCurrencyHeaderLyt" />
    <item type="id" name="conversionCurrencyHeaderUnderLineLyt" />
    <item type="id" name="conversionHeaderLayout" />
    <item type="id" name="conversionLeftCurrencyTextView" />
    <item type="id" name="conversionLeftCurrencyUnderlineView" />
    <item type="id" name="conversionLeftRateTextView" />
    <item type="id" name="conversionLeftSpreadFineTextView" />
    <item type="id" name="conversionRightCurrencyTextView" />
    <item type="id" name="conversionRightCurrencyUnderlineView" />
    <item type="id" name="conversionRightFineTextView" />
    <item type="id" name="conversionTitleTextView" />
    <item type="id" name="conversionTotalAmountCurrencyTextView" />
    <item type="id" name="conversionTotalPayTitleTextView" />
    <item type="id" name="conversion_ic" />
    <item type="id" name="conversion_options_carousel_text" />
    <item type="id" name="conversion_spread_tv" />
    <item type="id" name="coordinator" />
    <item type="id" name="copyright" />
    <item type="id" name="countryCheck" />
    <item type="id" name="country_picker_recycler_view" />
    <item type="id" name="country_search_view" />
    <item type="id" name="country_selected_cb" />
    <item type="id" name="cover_iv" />
    <item type="id" name="create_shortcut" />
    <item type="id" name="credit_offer_description_text" />
    <item type="id" name="crypto_consent_got_it_action_button" />
    <item type="id" name="crypto_conversion_ic" />
    <item type="id" name="crypto_conversion_rate_tv" />
    <item type="id" name="crypto_conversion_spread_tv" />
    <item type="id" name="crypto_currency_conversion_layout" />
    <item type="id" name="crypto_full_terms_and_conditions_link_tv" />
    <item type="id" name="crypto_progress_iv" />
    <item type="id" name="crypto_refunds_info" />
    <item type="id" name="crypto_refunds_title" />
    <item type="id" name="crypto_sales_info" />
    <item type="id" name="crypto_sales_title" />
    <item type="id" name="crypto_sell_info_text_one" />
    <item type="id" name="crypto_sell_info_text_two" />
    <item type="id" name="crypto_sell_title" />
    <item type="id" name="crypto_selling_amount_tv" />
    <item type="id" name="crypto_terms_scroll_view" />
    <item type="id" name="crypto_wrapper_72hour" />
    <item type="id" name="currency_conversion_layout" />
    <item type="id" name="current_price" />
    <item type="id" name="current_time_tv" />
    <item type="id" name="customPanel" />
    <item type="id" name="custom_balance_toggle_switch_view" />
    <item type="id" name="dataBinding" />
    <item type="id" name="date_picker_actions" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="delete" />
    <item type="id" name="des" />
    <item type="id" name="desc" />
    <item type="id" name="description" />
    <item type="id" name="descriptionTextView" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="detailImageView" />
    <item type="id" name="deviceIdTextView" />
    <item type="id" name="dialog_button" />
    <item type="id" name="dialog_description_tv" />
    <item type="id" name="dialog_negative_tv" />
    <item type="id" name="dialog_pb" />
    <item type="id" name="dialog_positive_tv" />
    <item type="id" name="dialog_title_tv" />
    <item type="id" name="dialog_view" />
    <item type="id" name="discord" />
    <item type="id" name="discord_btn" />
    <item type="id" name="discount_amount_textview" />
    <item type="id" name="discount_label" />
    <item type="id" name="diver" />
    <item type="id" name="divide_line" />
    <item type="id" name="dividerLine" />
    <item type="id" name="divider_card_info" />
    <item type="id" name="divider_zip_code" />
    <item type="id" name="drawerContentContainer" />
    <item type="id" name="drawerLayout" />
    <item type="id" name="drawer_layout" />
    <item type="id" name="dummy_anchor_view" />
    <item type="id" name="ec_token_text" />
    <item type="id" name="ed_app_search" />
    <item type="id" name="editText_card_number" />
    <item type="id" name="editText_csc" />
    <item type="id" name="editText_exp_date" />
    <item type="id" name="editText_zip_code" />
    <item type="id" name="edit_query" />
    <item type="id" name="email" />
    <item type="id" name="emailErrorView" />
    <item type="id" name="emailTextInputEditText" />
    <item type="id" name="emailTextInputLayout" />
    <item type="id" name="email_btn" />
    <item type="id" name="email_error_tv" />
    <item type="id" name="email_et" />
    <item type="id" name="email_report_tv" />
    <item type="id" name="email_tv" />
    <item type="id" name="empty_icon" />
    <item type="id" name="empty_layout_kit" />
    <item type="id" name="empty_text" />
    <item type="id" name="empty_tip" />
    <item type="id" name="empty_view" />
    <item type="id" name="empty_view_text" />
    <item type="id" name="end_horizontal_guide" />
    <item type="id" name="end_line_break" />
    <item type="id" name="end_padder" />
    <item type="id" name="errorImageView" />
    <item type="id" name="errorPaddingInputLayout" />
    <item type="id" name="errorTextView" />
    <item type="id" name="errorTitle" />
    <item type="id" name="error_code" />
    <item type="id" name="error_dialog_title" />
    <item type="id" name="error_message" />
    <item type="id" name="error_state_img" />
    <item type="id" name="error_text" />
    <item type="id" name="error_tv" />
    <item type="id" name="et_detail" />
    <item type="id" name="et_email" />
    <item type="id" name="et_input" />
    <item type="id" name="exit_button_image" />
    <item type="id" name="exo_check" />
    <item type="id" name="exo_icon" />
    <item type="id" name="exo_main_text" />
    <item type="id" name="exo_settings_listview" />
    <item type="id" name="exo_sub_text" />
    <item type="id" name="exo_text" />
    <item type="id" name="exo_track_selection_view" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_error_tv" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="expired_time" />
    <item type="id" name="fb_login_button" />
    <item type="id" name="feedback" />
    <item type="id" name="feedback_container" />
    <item type="id" name="file_name_et" />
    <item type="id" name="fingerprint_description" />
    <item type="id" name="fingerprint_error" />
    <item type="id" name="fingerprint_icon" />
    <item type="id" name="fingerprint_subtitle" />
    <item type="id" name="float_ball_des" />
    <item type="id" name="float_ball_permission" />
    <item type="id" name="float_ball_permission_desc" />
    <item type="id" name="float_ball_permission_iv" />
    <item type="id" name="float_ball_title" />
    <item type="id" name="float_introduce" />
    <item type="id" name="footer_container" />
    <item type="id" name="forgotLoginButton" />
    <item type="id" name="forgot_pwd_tv" />
    <item type="id" name="fragmentBgdMaskLayout" />
    <item type="id" name="fragmentBottomSheetLayout" />
    <item type="id" name="fragment_bgd_mask_layout" />
    <item type="id" name="fragment_bottom_sheet_layout" />
    <item type="id" name="fragment_container_view_tag" />
    <item type="id" name="free_tag" />
    <item type="id" name="free_try_logo" />
    <item type="id" name="free_try_top_bg" />
    <item type="id" name="fullPopupContainer" />
    <item type="id" name="full_loading_view" />
    <item type="id" name="get_it_to" />
    <item type="id" name="get_new_code" />
    <item type="id" name="get_started_btn" />
    <item type="id" name="ghost_view" />
    <item type="id" name="ghost_view_holder" />
    <item type="id" name="glide_custom_view_target_tag" />
    <item type="id" name="go_abox" />
    <item type="id" name="go_to_settings" />
    <item type="id" name="goods_des" />
    <item type="id" name="goods_name" />
    <item type="id" name="google_play" />
    <item type="id" name="google_play_tv" />
    <item type="id" name="gp_sign_in_button" />
    <item type="id" name="grand_total_amount_textview" />
    <item type="id" name="group_divider" />
    <item type="id" name="guide" />
    <item type="id" name="guideline" />
    <item type="id" name="headerView" />
    <item type="id" name="header_container" />
    <item type="id" name="header_text" />
    <item type="id" name="header_title" />
    <item type="id" name="helpDecTextview" />
    <item type="id" name="helpLabelTextView" />
    <item type="id" name="hide_immediately" />
    <item type="id" name="history_icon" />
    <item type="id" name="history_list" />
    <item type="id" name="home" />
    <item type="id" name="homeBgdMaskLayout" />
    <item type="id" name="horizontal_guide" />
    <item type="id" name="ic_close" />
    <item type="id" name="ic_logo" />
    <item type="id" name="ic_menu" />
    <item type="id" name="ic_vip_icon" />
    <item type="id" name="icon" />
    <item type="id" name="icon_back" />
    <item type="id" name="icon_frame" />
    <item type="id" name="icon_group" />
    <item type="id" name="icon_id" />
    <item type="id" name="im_main" />
    <item type="id" name="image" />
    <item type="id" name="imageClose" />
    <item type="id" name="imageView" />
    <item type="id" name="imageViewBackButton" />
    <item type="id" name="imageView_card_error" />
    <item type="id" name="imageView_payment_source_border" />
    <item type="id" name="imageView_zip_error" />
    <item type="id" name="image_view" />
    <item type="id" name="image_wrapper" />
    <item type="id" name="import_app_fl" />
    <item type="id" name="indicate_iv" />
    <item type="id" name="info" />
    <item type="id" name="initialProfilePicTextView" />
    <item type="id" name="insurance_amount_textview" />
    <item type="id" name="insurance_label" />
    <item type="id" name="interstitial_control_button" />
    <item type="id" name="interstitial_control_view" />
    <item type="id" name="invalid_shipping_method_tv" />
    <item type="id" name="invalid_shipping_tv" />
    <item type="id" name="is_free" />
    <item type="id" name="issuerImageView" />
    <item type="id" name="item_description_tv" />
    <item type="id" name="item_details_container" />
    <item type="id" name="item_icon" />
    <item type="id" name="item_name" />
    <item type="id" name="item_name_tv" />
    <item type="id" name="item_price_tv" />
    <item type="id" name="item_total_amount_textview" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="iv_add" />
    <item type="id" name="iv_app_icon" />
    <item type="id" name="iv_back" />
    <item type="id" name="iv_close" />
    <item type="id" name="iv_delete" />
    <item type="id" name="iv_image" />
    <item type="id" name="iv_logo" />
    <item type="id" name="iv_report_pic" />
    <item type="id" name="iv_search" />
    <item type="id" name="iv_tip" />
    <item type="id" name="iv_vm_start_logo" />
    <item type="id" name="iv_widget_preview" />
    <item type="id" name="l_header" />
    <item type="id" name="label" />
    <item type="id" name="label_72_hours" />
    <item type="id" name="last_four_digits_tv" />
    <item type="id" name="lastnameEditText" />
    <item type="id" name="launcher" />
    <item type="id" name="layout_app_info" />
    <item type="id" name="layout_dialog_button" />
    <item type="id" name="layout_dialog_custom_container" />
    <item type="id" name="layout_search_edit" />
    <item type="id" name="layout_search_icon" />
    <item type="id" name="learn_more_button" />
    <item type="id" name="left_feedback" />
    <item type="id" name="left_float_iv" />
    <item type="id" name="line" />
    <item type="id" name="line1" />
    <item type="id" name="line2" />
    <item type="id" name="line3" />
    <item type="id" name="line_break" />
    <item type="id" name="line_item_layout" />
    <item type="id" name="linearLayout_recommendation_container" />
    <item type="id" name="link_text" />
    <item type="id" name="list" />
    <item type="id" name="list5" />
    <item type="id" name="listView" />
    <item type="id" name="list_item" />
    <item type="id" name="list_selector_card_view" />
    <item type="id" name="loadProgress" />
    <item type="id" name="load_more_load_complete_view" />
    <item type="id" name="load_more_load_end_view" />
    <item type="id" name="load_more_load_fail_view" />
    <item type="id" name="load_more_loading_view" />
    <item type="id" name="loadingHeaderView" />
    <item type="id" name="loadingView" />
    <item type="id" name="loading_gift_image" />
    <item type="id" name="loading_layout_kit" />
    <item type="id" name="loading_message_tv" />
    <item type="id" name="loading_progress" />
    <item type="id" name="loading_progress_iv" />
    <item type="id" name="loading_spinner_parent_container" />
    <item type="id" name="loading_spinner_view" />
    <item type="id" name="loading_text" />
    <item type="id" name="loading_view" />
    <item type="id" name="loadview" />
    <item type="id" name="lock_manager" />
    <item type="id" name="login_btn" />
    <item type="id" name="login_with_email" />
    <item type="id" name="login_with_facebook" />
    <item type="id" name="login_with_google" />
    <item type="id" name="logo" />
    <item type="id" name="logo_iv" />
    <item type="id" name="logout" />
    <item type="id" name="logout_progress_spinner" />
    <item type="id" name="lottie_layer_name" />
    <item type="id" name="lottie_play" />
    <item type="id" name="main_container" />
    <item type="id" name="marqueeTextView" />
    <item type="id" name="masked" />
    <item type="id" name="material_clock_display" />
    <item type="id" name="material_clock_face" />
    <item type="id" name="material_clock_hand" />
    <item type="id" name="material_clock_period_am_button" />
    <item type="id" name="material_clock_period_pm_button" />
    <item type="id" name="material_clock_period_toggle" />
    <item type="id" name="material_hour_text_input" />
    <item type="id" name="material_hour_tv" />
    <item type="id" name="material_label" />
    <item type="id" name="material_minute_text_input" />
    <item type="id" name="material_minute_tv" />
    <item type="id" name="material_textinput_timepicker" />
    <item type="id" name="material_timepicker_cancel_button" />
    <item type="id" name="material_timepicker_container" />
    <item type="id" name="material_timepicker_edit_text" />
    <item type="id" name="material_timepicker_mode_button" />
    <item type="id" name="material_timepicker_ok_button" />
    <item type="id" name="material_timepicker_view" />
    <item type="id" name="material_value_index" />
    <item type="id" name="media_actions" />
    <item type="id" name="message" />
    <item type="id" name="message_textview" />
    <item type="id" name="micro_permission" />
    <item type="id" name="micro_permission_desc" />
    <item type="id" name="micro_permission_iv" />
    <item type="id" name="middleHorizontalGuideline" />
    <item type="id" name="middleVerticalGuideline" />
    <item type="id" name="month_grid" />
    <item type="id" name="month_navigation_bar" />
    <item type="id" name="month_navigation_fragment_toggle" />
    <item type="id" name="month_navigation_next" />
    <item type="id" name="month_navigation_previous" />
    <item type="id" name="month_title" />
    <item type="id" name="more_item_icon" />
    <item type="id" name="more_item_name" />
    <item type="id" name="more_payment_method_container" />
    <item type="id" name="motion_base" />
    <item type="id" name="mrec_ad_view_container" />
    <item type="id" name="mrec_control_button" />
    <item type="id" name="mrec_control_view" />
    <item type="id" name="mtrl_anchor_parent" />
    <item type="id" name="mtrl_calendar_day_selector_frame" />
    <item type="id" name="mtrl_calendar_days_of_week" />
    <item type="id" name="mtrl_calendar_frame" />
    <item type="id" name="mtrl_calendar_main_pane" />
    <item type="id" name="mtrl_calendar_months" />
    <item type="id" name="mtrl_calendar_selection_frame" />
    <item type="id" name="mtrl_calendar_text_input_frame" />
    <item type="id" name="mtrl_calendar_year_selector_frame" />
    <item type="id" name="mtrl_card_checked_layer_id" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="mtrl_motion_snapshot_view" />
    <item type="id" name="mtrl_picker_fullscreen" />
    <item type="id" name="mtrl_picker_header" />
    <item type="id" name="mtrl_picker_header_selection_text" />
    <item type="id" name="mtrl_picker_header_title_and_selection" />
    <item type="id" name="mtrl_picker_header_toggle" />
    <item type="id" name="mtrl_picker_text_input_date" />
    <item type="id" name="mtrl_picker_text_input_range_end" />
    <item type="id" name="mtrl_picker_text_input_range_start" />
    <item type="id" name="mtrl_picker_title_text" />
    <item type="id" name="mtrl_view_tag_bottom_padding" />
    <item type="id" name="multiSelectgroup" />
    <item type="id" name="name" />
    <item type="id" name="nameEditText" />
    <item type="id" name="name_tv" />
    <item type="id" name="native_ad_layout" />
    <item type="id" name="native_ad_view_container" />
    <item type="id" name="native_control_button" />
    <item type="id" name="native_control_view" />
    <item type="id" name="nav_controller_view_tag" />
    <item type="id" name="nav_host_fragment_container" />
    <item type="id" name="nav_view" />
    <item type="id" name="navigation_bar_item_active_indicator_view" />
    <item type="id" name="navigation_bar_item_icon_container" />
    <item type="id" name="navigation_bar_item_icon_view" />
    <item type="id" name="navigation_bar_item_labels_group" />
    <item type="id" name="navigation_bar_item_large_label_view" />
    <item type="id" name="navigation_bar_item_small_label_view" />
    <item type="id" name="navigation_fragment" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="navigation_home" />
    <item type="id" name="navigation_recording" />
    <item type="id" name="negative" />
    <item type="id" name="negative_tv" />
    <item type="id" name="nested_scroll" />
    <item type="id" name="nested_scroll_view" />
    <item type="id" name="newAddressRecyclerView" />
    <item type="id" name="new_version_found" />
    <item type="id" name="nextButton" />
    <item type="id" name="not_remind_checkbox" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="off" />
    <item type="id" name="okButton" />
    <item type="id" name="ok_button" />
    <item type="id" name="on" />
    <item type="id" name="onAttachStateChangeListener" />
    <item type="id" name="onDateChanged" />
    <item type="id" name="onboarding_wrapper" />
    <item type="id" name="order_history" />
    <item type="id" name="order_id" />
    <item type="id" name="order_price" />
    <item type="id" name="original_price" />
    <item type="id" name="other_plan" />
    <item type="id" name="otp_entry_1" />
    <item type="id" name="otp_entry_2" />
    <item type="id" name="otp_entry_3" />
    <item type="id" name="otp_entry_4" />
    <item type="id" name="otp_entry_5" />
    <item type="id" name="otp_entry_6" />
    <item type="id" name="otp_entry_barrier" />
    <item type="id" name="over_capture_text" />
    <item type="id" name="padding_view" />
    <item type="id" name="pager" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="partner_links_textview" />
    <item type="id" name="partners_content_view" />
    <item type="id" name="payLaterButton" />
    <item type="id" name="payPalAddressBookHeaderContainer" />
    <item type="id" name="payPalAddressBookInfoViewContainer" />
    <item type="id" name="payPalButton" />
    <item type="id" name="payPalConversionRateHeaderContainer" />
    <item type="id" name="payPalConversionRateInfoViewContainer" />
    <item type="id" name="payPalCreditButton" />
    <item type="id" name="payPalLegalAgreementsContainer" />
    <item type="id" name="payPalLogoutContainer" />
    <item type="id" name="payPalNewShippingAddressInfoViewContainer" />
    <item type="id" name="payPalNewShippingAddressSearchViewContainer" />
    <item type="id" name="payPalProfileHeaderContainer" />
    <item type="id" name="payPalProfileInfoContainer" />
    <item type="id" name="payPalRateProtectionHeaderContainer" />
    <item type="id" name="payPalRateProtectionInfoViewContainer" />
    <item type="id" name="payPalWordmarkImage" />
    <item type="id" name="payPal_crypto_terms_and_conditions_header_container" />
    <item type="id" name="payPal_shipping_method_container" />
    <item type="id" name="payPal_shipping_method_header_container" />
    <item type="id" name="pay_now_btn" />
    <item type="id" name="pay_now_container" />
    <item type="id" name="pay_now_progress_spinner" />
    <item type="id" name="paymentButtonContainer" />
    <item type="id" name="payment_bottom_sheet_id" />
    <item type="id" name="payment_method_title" />
    <item type="id" name="payment_source_background" />
    <item type="id" name="payment_source_monogram" />
    <item type="id" name="payment_source_optional_tv" />
    <item type="id" name="payment_source_title" />
    <item type="id" name="paypalLogoImageView" />
    <item type="id" name="paypal_billing_agreements_text_view" />
    <item type="id" name="paypal_buyer_id" />
    <item type="id" name="paypal_compound_top_banner" />
    <item type="id" name="paypal_container" />
    <item type="id" name="paypal_loading_button" />
    <item type="id" name="paypal_loading_button_spinner" />
    <item type="id" name="paypal_merchant_id" />
    <item type="id" name="paypal_sdk_version" />
    <item type="id" name="paypal_three_ds_info_view_container" />
    <item type="id" name="paypal_user_agreement_text_view" />
    <item type="id" name="paysheet_container" />
    <item type="id" name="paysheet_wrapper" />
    <item type="id" name="pbHeaderProgress" />
    <item type="id" name="permission_tip" />
    <item type="id" name="personalized_advertising_switch" />
    <item type="id" name="personalized_advertising_switch_textview" />
    <item type="id" name="photoViewContainer" />
    <item type="id" name="picker" />
    <item type="id" name="placeholderView" />
    <item type="id" name="play" />
    <item type="id" name="play_group" />
    <item type="id" name="play_status" />
    <item type="id" name="play_status_iv" />
    <item type="id" name="play_user_avatar" />
    <item type="id" name="play_voice_container" />
    <item type="id" name="positionPopupContainer" />
    <item type="id" name="positive" />
    <item type="id" name="positive_group" />
    <item type="id" name="positive_tv" />
    <item type="id" name="poweredByGoogleLabel" />
    <item type="id" name="pp_balance_tv" />
    <item type="id" name="pp_conversion_rate_tv" />
    <item type="id" name="pp_conversion_view_title" />
    <item type="id" name="pp_crypto_conversion_title" />
    <item type="id" name="pplogo2" />
    <item type="id" name="prefer_not_say" />
    <item type="id" name="preferred_fi_toggle_view" />
    <item type="id" name="preferred_fi_view_state" />
    <item type="id" name="preferred_text" />
    <item type="id" name="prefixText" />
    <item type="id" name="premium_icon" />
    <item type="id" name="premium_privilege" />
    <item type="id" name="price_container" />
    <item type="id" name="primary_split_balance_amount_tv" />
    <item type="id" name="primary_split_balance_container" />
    <item type="id" name="primary_split_balance_error_img" />
    <item type="id" name="primary_split_balance_error_tv" />
    <item type="id" name="primary_split_balance_info_img" />
    <item type="id" name="primary_split_balance_toggle" />
    <item type="id" name="privacy_policy" />
    <item type="id" name="privacy_policy_switch" />
    <item type="id" name="privacy_policy_switch_textview" />
    <item type="id" name="privacy_policy_tv" />
    <item type="id" name="privilege_sub_title" />
    <item type="id" name="privilege_subtitle" />
    <item type="id" name="privilege_title" />
    <item type="id" name="profilePicCircleImageView" />
    <item type="id" name="profilePicLayout" />
    <item type="id" name="progress" />
    <item type="id" name="progressBar" />
    <item type="id" name="progressBar_save_address" />
    <item type="id" name="progress_bar" />
    <item type="id" name="progress_bar_layout" />
    <item type="id" name="progress_bg" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="progress_spinner" />
    <item type="id" name="progress_value" />
    <item type="id" name="psImageView" />
    <item type="id" name="pwd_et" />
    <item type="id" name="pwd_tv" />
    <item type="id" name="pypl_add_card_zip" />
    <item type="id" name="pypl_addcard_address_line_2" />
    <item type="id" name="pypl_addcard_address_street" />
    <item type="id" name="pypl_addcard_address_title" />
    <item type="id" name="pypl_addcard_banner" />
    <item type="id" name="pypl_addcard_button" />
    <item type="id" name="pypl_addcard_card_number" />
    <item type="id" name="pypl_addcard_city" />
    <item type="id" name="pypl_addcard_city_zip_barrier" />
    <item type="id" name="pypl_addcard_cvv" />
    <item type="id" name="pypl_addcard_cvv_expiry_barrier" />
    <item type="id" name="pypl_addcard_expirity" />
    <item type="id" name="pypl_addcard_last_name" />
    <item type="id" name="pypl_addcard_name" />
    <item type="id" name="pypl_addcard_state" />
    <item type="id" name="radio" />
    <item type="id" name="rateProtectionBackArrowImageView" />
    <item type="id" name="rateProtectionBottomSheetLayout" />
    <item type="id" name="rateProtectionTitleTextView" />
    <item type="id" name="rate_protection_content" />
    <item type="id" name="recommend_clone_app_list" />
    <item type="id" name="recommend_view" />
    <item type="id" name="record_animation" />
    <item type="id" name="record_container" />
    <item type="id" name="record_group" />
    <item type="id" name="record_rv" />
    <item type="id" name="recycler" />
    <item type="id" name="recyclerView" />
    <item type="id" name="recycler_view" />
    <item type="id" name="register_tv" />
    <item type="id" name="renew" />
    <item type="id" name="replay" />
    <item type="id" name="report_ad_button" />
    <item type="id" name="report_drawn" />
    <item type="id" name="resendInfoButton" />
    <item type="id" name="reset_iv" />
    <item type="id" name="retry_layout_kit" />
    <item type="id" name="rewarded_control_button" />
    <item type="id" name="rewarded_control_view" />
    <item type="id" name="rewarded_interstitial_control_button" />
    <item type="id" name="rewarded_interstitial_control_view" />
    <item type="id" name="right_feedback" />
    <item type="id" name="right_float_iv" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="right_text" />
    <item type="id" name="rights_link_tv" />
    <item type="id" name="root_view" />
    <item type="id" name="row_index_key" />
    <item type="id" name="rv_app_list" />
    <item type="id" name="save" />
    <item type="id" name="saveNewAddressButton" />
    <item type="id" name="save_btn" />
    <item type="id" name="save_fl" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_overlay_view" />
    <item type="id" name="saved_recordings" />
    <item type="id" name="screenshots" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="scrollView_address_confirmation" />
    <item type="id" name="scroll_view" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_layout" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="secondary_split_balance_amount_tv" />
    <item type="id" name="secondary_split_balance_container" />
    <item type="id" name="secondary_split_balance_error_img" />
    <item type="id" name="secondary_split_balance_error_tv" />
    <item type="id" name="secondary_split_balance_toggle" />
    <item type="id" name="seek_bar" />
    <item type="id" name="seekbar" />
    <item type="id" name="seekbar_value" />
    <item type="id" name="select" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="select_premium_plan" />
    <item type="id" name="selected_flag_iv" />
    <item type="id" name="selection_type" />
    <item type="id" name="selectradiogroup" />
    <item type="id" name="setting" />
    <item type="id" name="setup_wizard_layout" />
    <item type="id" name="share" />
    <item type="id" name="share_fl" />
    <item type="id" name="ship_to_address_line_one_tv" />
    <item type="id" name="ship_to_tv" />
    <item type="id" name="shipping_and_handling_amount_textview" />
    <item type="id" name="shipping_discount_amount_textview" />
    <item type="id" name="shipping_discount_label" />
    <item type="id" name="shipping_info_container" />
    <item type="id" name="shipping_info_iv" />
    <item type="id" name="shipping_label" />
    <item type="id" name="shipping_method__selector_card_view" />
    <item type="id" name="shipping_method_back_arrow_img" />
    <item type="id" name="shipping_method_container" />
    <item type="id" name="shipping_method_loading_tv" />
    <item type="id" name="shipping_method_selected_cb" />
    <item type="id" name="shipping_method_tv" />
    <item type="id" name="shipping_methods_recyclerView" />
    <item type="id" name="shipping_price_tv" />
    <item type="id" name="shortcut" />
    <item type="id" name="show_mrec_button" />
    <item type="id" name="show_native_button" />
    <item type="id" name="show_product_details_arrow" />
    <item type="id" name="sign_in_btn" />
    <item type="id" name="size" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="something_not_right" />
    <item type="id" name="space1" />
    <item type="id" name="space2" />
    <item type="id" name="space3" />
    <item type="id" name="space4" />
    <item type="id" name="spaceBetweenStateAndZipcode" />
    <item type="id" name="spacer" />
    <item type="id" name="speak_btn" />
    <item type="id" name="special_effects_controller_view_tag" />
    <item type="id" name="spinner" />
    <item type="id" name="splitNameLayout" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="ss_submitAuthenticationButton" />
    <item type="id" name="start_horizontal_guide" />
    <item type="id" name="start_voice_change" />
    <item type="id" name="stateEditText" />
    <item type="id" name="stateZipcodeLayout" />
    <item type="id" name="status_bar_latest_event_content" />
    <item type="id" name="status_textview" />
    <item type="id" name="statusbarutil_fake_status_bar_view" />
    <item type="id" name="statusbarutil_translucent_view" />
    <item type="id" name="step_1_subtitle" />
    <item type="id" name="step_1_title" />
    <item type="id" name="step_2_subtitle" />
    <item type="id" name="step_2_title" />
    <item type="id" name="step_one_group" />
    <item type="id" name="step_two_group" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submitAuthenticationButton" />
    <item type="id" name="submit_area" />
    <item type="id" name="subtitle" />
    <item type="id" name="subtotal_label" />
    <item type="id" name="suffixText" />
    <item type="id" name="switchWidget" />
    <item type="id" name="switch_bottom_padding" />
    <item type="id" name="switch_checkout_text" />
    <item type="id" name="tag_accessibility_actions" />
    <item type="id" name="tag_accessibility_clickable_spans" />
    <item type="id" name="tag_accessibility_heading" />
    <item type="id" name="tag_accessibility_pane_title" />
    <item type="id" name="tag_layout_helper_bg" />
    <item type="id" name="tag_on_apply_window_listener" />
    <item type="id" name="tag_on_receive_content_listener" />
    <item type="id" name="tag_on_receive_content_mime_types" />
    <item type="id" name="tag_screen_reader_focusable" />
    <item type="id" name="tag_state_description" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="tag_window_insets_animation_callback" />
    <item type="id" name="tax_amount_textview" />
    <item type="id" name="tax_label" />
    <item type="id" name="telegram" />
    <item type="id" name="terms_of_service" />
    <item type="id" name="terms_of_service_tv" />
    <item type="id" name="test_checkbox_android_button_tint" />
    <item type="id" name="test_checkbox_app_button_tint" />
    <item type="id" name="test_radiobutton_android_button_tint" />
    <item type="id" name="test_radiobutton_app_button_tint" />
    <item type="id" name="text" />
    <item type="id" name="text1" />
    <item type="id" name="text2" />
    <item type="id" name="textInputEditText" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="textView" />
    <item type="id" name="textViewFeelings" />
    <item type="id" name="textView_address_match_header" />
    <item type="id" name="textView_address_match_subheader" />
    <item type="id" name="textView_card_error" />
    <item type="id" name="textView_input_address_header" />
    <item type="id" name="textView_input_address_line_1" />
    <item type="id" name="textView_input_address_line_2" />
    <item type="id" name="textView_input_address_line_3" />
    <item type="id" name="textView_rec_address_header" />
    <item type="id" name="textView_rec_address_line_1" />
    <item type="id" name="textView_rec_address_line_2" />
    <item type="id" name="textView_rec_address_line_3" />
    <item type="id" name="textView_zip_error" />
    <item type="id" name="textWatcher" />
    <item type="id" name="text_additional_info" />
    <item type="id" name="text_input_end_icon" />
    <item type="id" name="text_input_error_icon" />
    <item type="id" name="text_input_start_icon" />
    <item type="id" name="text_otp_login_title" />
    <item type="id" name="text_otp_phone_number" />
    <item type="id" name="text_otp_phone_number_drop_down" />
    <item type="id" name="text_send_code" />
    <item type="id" name="textbody" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="textinput_placeholder" />
    <item type="id" name="textinput_prefix_text" />
    <item type="id" name="textinput_suffix_text" />
    <item type="id" name="tg_btn" />
    <item type="id" name="three_ds_back_arrow_img" />
    <item type="id" name="three_ds_bottom_sheet_layout" />
    <item type="id" name="three_ds_header_container" />
    <item type="id" name="three_ds_progress_iv" />
    <item type="id" name="three_ds_title_txt" />
    <item type="id" name="three_ds_web_view" />
    <item type="id" name="til_card_number" />
    <item type="id" name="til_csc" />
    <item type="id" name="til_exp_date" />
    <item type="id" name="til_zip_code" />
    <item type="id" name="time" />
    <item type="id" name="time_des" />
    <item type="id" name="tip" />
    <item type="id" name="tip_group" />
    <item type="id" name="tip_tv" />
    <item type="id" name="title" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="titleTextView" />
    <item type="id" name="title_container" />
    <item type="id" name="title_label" />
    <item type="id" name="title_template" />
    <item type="id" name="title_text" />
    <item type="id" name="title_textview" />
    <item type="id" name="toast_alert_view" />
    <item type="id" name="toast_icon" />
    <item type="id" name="toast_root" />
    <item type="id" name="toast_text" />
    <item type="id" name="toggle_switch" />
    <item type="id" name="toggle_switch_layout" />
    <item type="id" name="toggle_text" />
    <item type="id" name="toolbar" />
    <item type="id" name="toolbarButton" />
    <item type="id" name="topPanel" />
    <item type="id" name="top_banner_container" />
    <item type="id" name="top_container" />
    <item type="id" name="total_time_tv" />
    <item type="id" name="touch_outside" />
    <item type="id" name="transactionDetailsBackArrowImageView" />
    <item type="id" name="transactionDetailsBgdMaskLayout" />
    <item type="id" name="transactionDetailsBottomSheetLayout" />
    <item type="id" name="transactionDetailsHeaderLayout" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tv_add" />
    <item type="id" name="tv_app_name" />
    <item type="id" name="tv_app_search" />
    <item type="id" name="tv_cancel" />
    <item type="id" name="tv_confirm" />
    <item type="id" name="tv_content" />
    <item type="id" name="tv_delete" />
    <item type="id" name="tv_loading_text" />
    <item type="id" name="tv_pager_indicator" />
    <item type="id" name="tv_prompt" />
    <item type="id" name="tv_save" />
    <item type="id" name="tv_submit" />
    <item type="id" name="tv_text" />
    <item type="id" name="tv_title" />
    <item type="id" name="tv_username" />
    <item type="id" name="typeAddress" />
    <item type="id" name="unchecked" />
    <item type="id" name="understand_and_confirm_button" />
    <item type="id" name="up" />
    <item type="id" name="update_content" />
    <item type="id" name="update_immediately" />
    <item type="id" name="upgrade" />
    <item type="id" name="usePasswordButton" />
    <item type="id" name="userProfileBackArrowImageView" />
    <item type="id" name="userProfileBgdMaskLayout" />
    <item type="id" name="userProfileBottomSheetLayout" />
    <item type="id" name="userProfileCircleImageView" />
    <item type="id" name="userProfileCircleLayout" />
    <item type="id" name="userProfileEmailTextView" />
    <item type="id" name="userProfileInitialCircleTextView" />
    <item type="id" name="userProfileLegalAgreementsTextView" />
    <item type="id" name="userProfileLogoutButton" />
    <item type="id" name="userProfileNameTextView" />
    <item type="id" name="user_id" />
    <item type="id" name="user_info_container" />
    <item type="id" name="user_protocol" />
    <item type="id" name="valueTv" />
    <item type="id" name="verify_code_et" />
    <item type="id" name="verify_code_tv" />
    <item type="id" name="version_name" />
    <item type="id" name="vertical_guide" />
    <item type="id" name="view" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="view_transition" />
    <item type="id" name="view_tree_lifecycle_owner" />
    <item type="id" name="view_tree_on_back_pressed_dispatcher_owner" />
    <item type="id" name="view_tree_saved_state_registry_owner" />
    <item type="id" name="view_tree_view_model_store_owner" />
    <item type="id" name="vip" />
    <item type="id" name="visible_removing_fragment_view_tag" />
    <item type="id" name="vm_float_window" />
    <item type="id" name="voice_bg" />
    <item type="id" name="voice_des3" />
    <item type="id" name="voice_experience_container" />
    <item type="id" name="voice_group" />
    <item type="id" name="voice_iv" />
    <item type="id" name="voice_list" />
    <item type="id" name="voice_list_title" />
    <item type="id" name="voice_name_tv" />
    <item type="id" name="voice_progress" />
    <item type="id" name="voice_rv" />
    <item type="id" name="voice_tips" />
    <item type="id" name="vv_divider" />
    <item type="id" name="waring_tips" />
    <item type="id" name="warningIndicator" />
    <item type="id" name="web_view" />
    <item type="id" name="web_view_loading_progress" />
    <item type="id" name="webviewUi" />
    <item type="id" name="whiteListCheckboxHolder" />
    <item type="id" name="whyInfoDecTextview" />
    <item type="id" name="whyInfoLableTextview" />
    <item type="id" name="why_72" />
    <item type="id" name="widget_content" />
    <item type="id" name="wrapper_72hour" />
    <item type="id" name="xpopup_divider" />
    <item type="id" name="xpopup_divider1" />
    <item type="id" name="xpopup_divider2" />
    <item type="id" name="youtube" />
    <item type="id" name="zero_corner_chip" />
    <item type="id" name="zipEditText" />
    <item type="id" name="textViewTitle" />
    <item type="id" name="editTextUnlockCode" />
    <item type="id" name="buttonUnlock" />
</resources>
