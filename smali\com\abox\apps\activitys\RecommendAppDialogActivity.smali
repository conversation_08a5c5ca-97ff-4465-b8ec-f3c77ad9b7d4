.class public final Lcom/abox/apps/activitys/RecommendAppDialogActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0008\u0010\u0004\u001a\u00020\u0005H\u0016J\u0010\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0008H\u0014J\u0008\u0010\t\u001a\u00020\u0005H\u0014J\u0008\u0010\n\u001a\u00020\u0005H\u0014J\u0008\u0010\u000b\u001a\u00020\u0005H\u0014\u00a8\u0006\u000c"
    }
    d2 = {
        "Lcom/abox/apps/activitys/RecommendAppDialogActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;",
        "()V",
        "finish",
        "",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "onDestroy",
        "onPause",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    return-void
.end method

.method private static final asBinder(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 7

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/abox/apps/activitys/FreeTryActivity;->getDefaultImpl:Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;

    const/4 v2, 0x4

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xc

    const/4 v6, 0x0

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;->asInterface(Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;Landroid/content/Context;ILjava/lang/String;IILjava/lang/Object;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->finish()V

    return-void
.end method

.method public static synthetic asInterface(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->read(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic getDefaultImpl(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->asBinder(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final onTransact(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 1

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string/jumbo v0, "vphClose"

    invoke-virtual {p1, v0}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object p1, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    const-string v0, "closeVipPopupHome"

    invoke-virtual {p1, v0}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->finish()V

    return-void
.end method

.method private static final read(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 7

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/abox/apps/activitys/FreeTryActivity;->getDefaultImpl:Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;

    const/4 v2, 0x4

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xc

    const/4 v6, 0x0

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;->asInterface(Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;Landroid/content/Context;ILjava/lang/String;IILjava/lang/Object;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->finish()V

    return-void
.end method

.method public static synthetic setDefaultImpl(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->onTransact(Lcom/abox/apps/activitys/RecommendAppDialogActivity;Landroid/view/View;)V

    return-void
.end method


# virtual methods
.method public finish()V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, v0, v0}, Landroid/app/Activity;->overridePendingTransition(II)V

    invoke-super {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method protected getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;
    .locals 1
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->setDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method public onDestroy()V
    .locals 2

    invoke-super {p0}, Landroidx/appcompat/app/AppCompatActivity;->onDestroy()V

    sget-object v0, Lo/NotActiveException;->setDefaultImpl:Lo/NotActiveException;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lo/NotActiveException;->asBinder(Z)V

    return-void
.end method

.method public onPause()V
    .locals 1

    invoke-super {p0}, Landroidx/fragment/app/FragmentActivity;->onPause()V

    const/4 v0, 0x0

    invoke-virtual {p0, v0, v0}, Landroid/app/Activity;->overridePendingTransition(II)V

    return-void
.end method

.method protected onTransact()V
    .locals 6

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string/jumbo v1, "vipPopupHome"

    invoke-virtual {v0, v1}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    const-string/jumbo v1, "showVipPopupHome"

    invoke-virtual {v0, v1}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-static {p0, v0}, Lo/CloneNotSupportedException;->onTransact(Landroid/app/Activity;I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->asInterface:Landroid/widget/ImageView;

    new-instance v2, Lo/URLSpan;

    invoke-direct {v2, p0}, Lo/URLSpan;-><init>(Lcom/abox/apps/activitys/RecommendAppDialogActivity;)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onConnected:Landroid/widget/TextView;

    invoke-virtual {v1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    move-result-object v1

    const/16 v2, 0x10

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setFlags(I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onConnected:Landroid/widget/TextView;

    invoke-virtual {v1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    sget-object v1, Lo/LineNumberReader;->setDefaultImpl:Lo/LineNumberReader;

    invoke-virtual {v1}, Lo/LineNumberReader;->onTransact()Lcom/abox/apps/model/CommonConfig;

    move-result-object v2

    invoke-virtual {v2}, Lcom/abox/apps/model/CommonConfig;->getOriginalPrice()Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v3

    check-cast v3, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v3, v3, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onConnected:Landroid/widget/TextView;

    invoke-virtual {v3, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_0
    invoke-virtual {v1}, Lo/LineNumberReader;->onTransact()Lcom/abox/apps/model/CommonConfig;

    move-result-object v1

    invoke-virtual {v1}, Lcom/abox/apps/model/CommonConfig;->getCurrentPrice()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v2

    check-cast v2, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v2, v2, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->setDefaultImpl:Landroid/widget/TextView;

    if-nez v2, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_2
    :goto_0
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->IconCompatParcelizer:Landroid/widget/TextView;

    new-instance v2, Lo/Scene;

    invoke-direct {v2, p0}, Lo/Scene;-><init>(Lcom/abox/apps/activitys/RecommendAppDialogActivity;)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Landroidx/appcompat/app/AppCompatActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object v1

    iget v1, v1, Landroid/content/res/Configuration;->orientation:I

    sget v2, Lo/Cursor$ActionBar;->PlaybackStateCompat:I

    invoke-static {v2}, Lo/aln;->asInterface(I)I

    move-result v2

    sget v3, Lo/Cursor$ActionBar;->ComponentActivity:I

    invoke-static {v3}, Lo/aln;->asInterface(I)I

    move-result v3

    sget v4, Lo/Cursor$ActionBar;->MediaBrowserCompat$CustomActionResultReceiver:I

    invoke-static {v4}, Lo/aln;->asInterface(I)I

    move-result v4

    const/4 v5, 0x1

    if-ne v1, v5, :cond_3

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    invoke-virtual {v0}, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onTransact()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    invoke-static {v0, v2, v3, v2, v3}, Lo/alr;->setDefaultImpl(Landroid/view/View;IIII)V

    goto :goto_1

    :cond_3
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    invoke-virtual {v1}, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onTransact()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v1

    invoke-static {v1, v4, v0, v4, v0}, Lo/alr;->setDefaultImpl(Landroid/view/View;IIII)V

    :goto_1
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    invoke-virtual {v0}, Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;->onTransact()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    new-instance v1, Lo/ClickableSpan;

    invoke-direct {v1, p0}, Lo/ClickableSpan;-><init>(Lcom/abox/apps/activitys/RecommendAppDialogActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    sget-object v0, Lo/PrintWriter;->setDefaultImpl:Lo/PrintWriter;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lo/PrintWriter;->asInterface(J)V

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/RecommendAppDialogActivity;->getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendAppDialogBinding;

    move-result-object p1

    return-object p1
.end method
