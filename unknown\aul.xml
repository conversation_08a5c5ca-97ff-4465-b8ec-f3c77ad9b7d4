<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="#FF1A1A1A">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="App Unlock Required"
        android:textSize="24sp"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        android:layout_marginBottom="32dp"
        android:gravity="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Enter unlock code to continue:"
        android:textSize="16sp"
        android:textColor="#CCCCCC"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <EditText
        android:id="@+id/editTextUnlockCode"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:hint="Unlock Code"
        android:textColorHint="#888888"
        android:textColor="#FFFFFF"
        android:background="@drawable/edit_text_background"
        android:padding="16dp"
        android:layout_marginBottom="24dp"
        android:inputType="text"
        android:maxLines="1"
        android:imeOptions="actionDone" />

    <Button
        android:id="@+id/buttonUnlock"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="UNLOCK"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@drawable/button_background"
        android:layout_marginTop="16dp" />

</LinearLayout>
