.class public final Lcom/abox/apps/activitys/UnlockActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivityUnlockBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0018\u0000 \t2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\tB\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0006H\u0014J\u0008\u0010\u0007\u001a\u00020\u0008H\u0014\u00a8\u0006\n"
    }
    d2 = {
        "Lcom/abox/apps/activitys/UnlockActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivityUnlockBinding;",
        "()V",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final asInterface:Lcom/abox/apps/activitys/UnlockActivity$Application;
    .annotation build Lo/cbz;
    .end annotation
.end field

.field private static final UNLOCK_CODE:Ljava/lang/String; = "LOCK-5960"

.field private static final PREFS_NAME:Ljava/lang/String; = "unlock_prefs"

.field private static final KEY_ACTIVATED:Ljava/lang/String; = "activated"


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/activitys/UnlockActivity$Application;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/activitys/UnlockActivity$Application;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/activitys/UnlockActivity;->asInterface:Lcom/abox/apps/activitys/UnlockActivity$Application;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    return-void
.end method

.method private final checkUnlockCode(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "LOCK-5960"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method private final saveActivationStatus()V
    .locals 3

    const-string v0, "unlock_prefs"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    const-string v1, "activated"

    const/4 v2, 0x1

    invoke-interface {v0, v1, v2}, Landroid/content/SharedPreferences$Editor;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method private final proceedToMainApp()V
    .locals 2

    sget-object v0, Lcom/abox/apps/activitys/AcquirePermissionGuideActivity;->asInterface:Lcom/abox/apps/activitys/AcquirePermissionGuideActivity$Application;

    invoke-virtual {v0, p0}, Lcom/abox/apps/activitys/AcquirePermissionGuideActivity$Application;->asBinder(Landroid/content/Context;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method private final showErrorToast()V
    .locals 3

    const-string v0, "Invalid unlock code. Access denied."

    const/4 v1, 0x1

    invoke-static {p0, v0, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    return-void
.end method

.method public static final isActivated(Landroid/content/Context;)Z
    .locals 1
    .param p0    # Landroid/content/Context;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation runtime Lkotlin/jvm/JvmStatic;
    .end annotation

    sget-object v0, Lcom/abox/apps/activitys/UnlockActivity;->asInterface:Lcom/abox/apps/activitys/UnlockActivity$Application;

    invoke-virtual {v0, p0}, Lcom/abox/apps/activitys/UnlockActivity$Application;->isActivated(Landroid/content/Context;)Z

    move-result p0

    return p0
.end method

.method private static final onUnlockButtonClick(Lcom/abox/apps/activitys/UnlockActivity;Landroid/view/View;)V
    .locals 1

    const-string p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityUnlockBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityUnlockBinding;->editTextUnlockCode:Landroid/widget/EditText;

    invoke-virtual {p1}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lkotlin/text/StringsKt;->trim(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/abox/apps/activitys/UnlockActivity;->checkUnlockCode(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-direct {p0}, Lcom/abox/apps/activitys/UnlockActivity;->saveActivationStatus()V

    invoke-direct {p0}, Lcom/abox/apps/activitys/UnlockActivity;->proceedToMainApp()V

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lcom/abox/apps/activitys/UnlockActivity;->showErrorToast()V

    :goto_0
    return-void
.end method


# virtual methods
.method protected asInterface(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityUnlockBinding;
    .locals 1
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1}, Lcom/abox/apps/databinding/ActivityUnlockBinding;->asInterface(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityUnlockBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method protected onTransact()V
    .locals 2

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityUnlockBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityUnlockBinding;->buttonUnlock:Landroid/widget/Button;

    new-instance v1, Lo/UnlockClickListener;

    invoke-direct {v1, p0}, Lo/UnlockClickListener;-><init>(Lcom/abox/apps/activitys/UnlockActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/UnlockActivity;->asInterface(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityUnlockBinding;

    move-result-object p1

    return-object p1
.end method

.method public static synthetic access$onUnlockButtonClick(Lcom/abox/apps/activitys/UnlockActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/UnlockActivity;->onUnlockButtonClick(Lcom/abox/apps/activitys/UnlockActivity;Landroid/view/View;)V

    return-void
.end method
