<?xml version="1.0" encoding="utf-8"?>
<manifest android:versionCode="24" android:versionName="1.1.9" android:compileSdkVersion="33" android:compileSdkVersionCodename="13" package="com.abox.apps" platformBuildVersionCode="33" platformBuildVersionName="13"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-sdk android:minSdkVersion="24" android:targetSdkVersion="33" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <permission android:name="com.abox.android.mg.checkenv" android:protectionLevel="signatureOrSystem" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="com.android.permission.GET_INSTALLED_APPS" />
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <package android:name="com.discord" />
        <package android:name="com.google.android.youtube" />
        <package android:name="org.telegram.messenger" />
        <package android:name="com.whatsapp" />
        <package android:name="com.android.vending" />
        <package android:name="com.dts.freefireth" />
        <package android:name="com.instagram.android" />
        <package android:name="com.facebook.orca" />
        <package android:name="com.mobile.legends" />
        <package android:name="com.discord" />
        <package android:name="com.whatsapp.w4b" />
        <package android:name="com.truecaller" />
        <package android:name="com.dts.freefiremax" />
        <package android:name="org.telegram.messenger" />
        <package android:name="com.roblox.client" />
        <package android:name="com.facebook.katana" />
        <package android:name="com.gbwhatsapp" />
        <package android:name="com.garena.game.codm" />
        <package android:name="com.facebook.lite" />
        <package android:name="com.snapchat.android" />
        <package android:name="com.tencent.ig" />
        <package android:name="com.lemon.lvoverseas" />
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />
        <package android:name="com.litatom.app" />
        <package android:name="com.imo.android.imoim" />
        <package android:name="com.zing.zalo" />
        <package android:name="sg.bigo.hellotalk" />
        <package android:name="net.wrightflyer.le.reality" />
        <package android:name="com.activision.callofduty.shooter" />
        <package android:name="com.yalla.yallagames" />
        <package android:name="com.garena.game.kgvn" />
        <package android:name="videoeditor.videorecorder.screenrecorder" />
        <package android:name="com.garena.game.kgth" />
        <package android:name="com.wejoy.weplay" />
        <package android:name="com.google.android.apps.tachyon" />
        <package android:name="com.je.supersus" />
        <package android:name="com.sec.android.app.voicenote" />
        <package android:name="com.android.soundrecorder" />
        <package android:name="com.instagram.lite" />
        <package android:name="com.splendapps.voicerec" />
        <package android:name="jp.naver.line.android" />
        <package android:name="com.mobilelegends.hwag" />
        <package android:name="com.jarvigames.viceonline" />
        <package android:name="com.paraspace.together" />
        <package android:name="in.mohalla.sharechat" />
        <package android:name="com.netease.newspike" />
        <package android:name="omegle.tv" />
        <package android:name="com.nexstreaming.app.kinemasterfree" />
        <package android:name="com.soulcastry.virtualdroid2" />
        <package android:name="com.riotgames.league.wildrift" />
        <package android:name="com.coloros.gamespaceui" />
        <package android:name="com.fmwhatsapp" />
        <package android:name="com.bluecord" />
        <package android:name="com.yowhatsapp" />
        <package android:name="org.telegram.messenger.web" />
        <package android:name="com.miraclegames.farlight84" />
        <package android:name="com.viber.voip" />
        <package android:name="com.wejoy.weplay.ar" />
        <package android:name="com.universe.messenger" />
        <package android:name="com.pubg.krmobile" />
        <package android:name="gg.turnip.android" />
        <package android:name="sg.bigo.live" />
        <package android:name="com.mbwhatsapp" />
        <package android:name="com.innersloth.spacemafia" />
        <package android:name="com.mxtech.videoplayer.ad" />
        <package android:name="com.camerasideas.instashot" />
        <package android:name="voicechanger.voiceeffects.soundeffects.voiceavatar" />
        <package android:name="com.WhatsApp2Plus" />
        <package android:name="com.imo.android.imoimhd" />
        <package android:name="ru.zdevs.zarchiver" />
        <package android:name="com.garena.game.lmjx" />
        <package android:name="com.spotify.music" />
        <package android:name="com.scee.psxandroid" />
        <package android:name="me.zepeto.main" />
        <package android:name="com.aeroinsta.android" />
        <package android:name="us.zoom.videomeetings" />
        <package android:name="com.frontrow.vlog" />
        <package android:name="com.oplus.dialer" />
        <package android:name="com.aero" />
        <package android:name="com.google.android.dialer" />
        <package android:name="com.media.bestrecorder.audiorecorder" />
        <package android:name="com.Gaggle.fun.GooseGooseDuck" />
        <package android:name="com.AgainstGravity.RecRoom" />
        <package android:name="screenrecorder.recorder.editor" />
        <package android:name="com.hecorat.screenrecorder.free" />
        <package android:name="com.google.android.apps.docs" />
        <package android:name="com.twitter.android" />
        <package android:name="com.eyecon.global" />
        <package android:name="com.bsbportal.music" />
        <package android:name="com.rsupport.mvagent" />
        <package android:name="com.apkpure.aegon" />
        <package android:name="com.pubg.imobile" />
        <package android:name="com.litatom.lite" />
        <package android:name="com.first75.voicerecorder2" />
        <package android:name="com.google.android.apps.youtube.music" />
        <package android:name="com.imo.android.imoimbeta" />
        <package android:name="com.starmakerinteractive.starmaker" />
        <package android:name="com.google.android.apps.subscriptions.red" />
        <package android:name="com.xiaomi.gameboosterglobal" />
        <package android:name="com.supercell.clashofclans" />
        <package android:name="com.google.android.videos" />
        <package android:name="com.heytap.games.forum" />
        <package android:name="com.playit.videoplayer" />
        <package android:name="com.flipkart.android" />
        <package android:name="com.mojang.minecraftpe" />
        <package android:name="lv.indycall.client" />
        <package android:name="com.miui.mediaeditor" />
        <package android:name="com.threesixteen.app" />
        <package android:name="com.miui.screenrecorder" />
        <package android:name="com.phonepe.app" />
        <package android:name="com.ofotech.app" />
        <package android:name="com.coloros.backuprestore" />
        <package android:name="com.samsung.android.app.notes" />
        <package android:name="org.thunderdog.challegram" />
        <package android:name="com.google.android.apps.translate" />
        <package android:name="com.google.android.play.games" />
        <package android:name="com.voicechanger.soundeffects.voice.effects.recorder" />
        <package android:name="walkie.talkie.among.us.friends" />
        <package android:name="com.google.android.contacts" />
        <package android:name="com.google.android.apps.photos" />
        <package android:name="com.hqinfosystem.callscreen" />
        <package android:name="com.google.android.apps.youtube.creator" />
        <package android:name="com.google.android.apps.magazines" />
        <package android:name="com.mobilelegends.mi" />
        <package android:name="com.microsoft.teams" />
        <package android:name="com.tencent.iglite" />
        <package android:name="com.amazon.appmanager" />
        <package android:name="com.baviux.voicechanger" />
        <package android:name="com.playmini.miniworld" />
        <package android:name="global.juscall.android" />
        <package android:name="com.facebook.mlite" />
        <package android:name="com.dywx.larkplayer" />
        <package android:name="com.simpler.dialer" />
        <package android:name="com.myairtelapp" />
        <package android:name="com.preff.kb.xm" />
        <package android:name="com.fun.share" />
        <package android:name="app.rbmain.a" />
        <package android:name="cn.xender" />
        <package android:name="com.gbwhatsapp3" />
        <package android:name="com.vaultmicro.camerafi.live" />
        <package android:name="com.wildlifestudios.free.online.games.suspects" />
        <package android:name="com.olzhas.carparking.multyplayer" />
        <package android:name="ru.unisamp_mobile.launcher" />
        <package android:name="com.truedevelopersstudio.automatictap.autoclicker" />
        <package android:name="com.google.android.apps.podcasts" />
        <package android:name="com.cloudflare.onedotonedotonedotone" />
        <package android:name="com.next.innovation.takatak" />
        <package android:name="com.mventus.selfcare.activity" />
        <package android:name="com.google.android.apps.dynamite" />
        <package android:name="com.zhiliaoapp.musically.go" />
        <package android:name="com.vrchat.mobile.playstore" />
        <package android:name="voicerecorder.audiorecorder.voice" />
        <package android:name="com.coffeebeanventures.easyvoicerecorder" />
        <package android:name="com.realmecomm.app" />
        <package android:name="com.proximabeta.mf.uamo" />
        <package android:name="net.kdt.pojavlaunch" />
        <package android:name="com.narvii.amino.master" />
        <package android:name="com.gspace.android" />
        <package android:name="com.google.android.gm" />
        <package android:name="com.baitu.qingshu" />
        <package android:name="com.miHoYo.GenshinImpact" />
        <package android:name="com.netease.partyglobal" />
        <package android:name="com.bandlab.bandlab" />
        <package android:name="com.king.candycrushsaga" />
        <package android:name="com.crazyhero.android" />
        <package android:name="com.imyfone.magicmic" />
        <package android:name="com.Rohit.IndianBikes" />
        <package android:name="com.voicemaker.android" />
        <package android:name="com.dts.freefireadv" />
        <package android:name="com.fingersoft.hillclimb" />
        <package android:name="tv.twitch.android.app" />
        <package android:name="com.google.android.music" />
        <package android:name="com.waxmoon.ma.gp" />
        <package android:name="org.mozilla.firefox" />
        <package android:name="jp.konami.pesam" />
        <package android:name="com.hellotalk" />
        <package android:name="com.xiaomi.glgm" />
        <package android:name="com.ludo.king" />
        <package android:name="in.mohalla.video" />
        <package android:name="com.iMe.android" />
        <package android:name="th.media.itsme" />
        <package android:name="com.gbox.android" />
        <package android:name="com.prism.live" />
        <package android:name="com.chat.ruletka" />
        <package android:name="com.fiya.android" />
        <package android:name="com.kwai.bulldog" />
        <package android:name="com.Fondi.Fondi" />
        <package android:name="com.F2Games.GBDE" />
        <package android:name="com.jio.myjio" />
        <package android:name="glip.gg" />
        <package android:name="com.blogspot.byterevapps.lollipopscreenrecorder" />
        <package android:name="com.ziipin.softkeyboard.iraq" />
        <package android:name="com.sound.voiceeffects.voicechanger" />
        <package android:name="in.amazon.mShop.android.shopping" />
        <package android:name="com.miniclip.eightballpool" />
        <package android:name="org.thoughtcrime.securesms" />
        <package android:name="com.bigwinepot.nwdn.international" />
        <package android:name="com.appzcloud.audioeditor" />
        <package android:name="com.camera.secretvideorecorder" />
        <package android:name="com.xenom.voice.dialer.unlimited.calls" />
        <package android:name="com.pamsys.lexisaudioeditor" />
        <package android:name="in.cricketexchange.app.cricketexchange" />
        <package android:name="com.KCCreations.FlippyBird2" />
        <package android:name="musicplayer.musicapps.music.mp3player" />
        <package android:name="cn.wps.xiaomi.abroad.lite" />
        <package android:name="com.ultra.camerahd.camera" />
        <package android:name="com.miui.android.fashiongallery" />
        <package android:name="com.supercell.clashroyale" />
        <package android:name="com.netease.idv.googleplay" />
        <package android:name="com.mobi.screenrecorder.durecorder" />
        <package android:name="com.voice.changer.recorder.effects.editor" />
        <package android:name="com.gbox.com.facebook.katana" />
        <package android:name="com.google.android.GoogleCameraEng" />
        <package android:name="recorder.screenrecorder.videoeditor" />
        <package android:name="com.alightcreative.motion" />
        <package android:name="tiktok.video.downloader.nowatermark.tiktokdownload" />
        <package android:name="com.calculator.lock.hide.photo.video" />
        <package android:name="com.imo.android.imoimlite" />
        <package android:name="vocal.remover.backing.tracks.acapella.extractor.song.maker" />
        <package android:name="com.eg.mimicry.horror.online" />
        <package android:name="free.tube.premium.advanced.tuber" />
        <package android:name="com.microsoft.xboxone.smartglass" />
        <package android:name="com.noodlecake.altosadventure" />
        <package android:name="com.sec.android.app.popupcalculator" />
        <package android:name="com.proximabeta.dn2.global" />
        <package android:name="kwai.kwaivideodownloader.savekwaivideo.nowatermark" />
        <package android:name="com.differencetenderwhite.skirt" />
        <package android:name="com.samsung.android.game.immersivemode" />
        <package android:name="vidma.screenrecorder.videorecorder.videoeditor.lite" />
        <package android:name="dje073.android.modernrecforge" />
        <package android:name="com.media.music.mp3.musicplayer" />
        <package android:name="com.nhstudio.icall.callios.iphonedialer" />
        <package android:name="com.starbattleroyale.play" />
        <package android:name="com.raytechnoto.glab.voicerecorder" />
        <package android:name="com.zrolee.mangoo" />
        <package android:name="com.callapp.contacts" />
        <package android:name="com.hitrolab.audioeditor" />
        <package android:name="com.xiaomi.midrop" />
        <package android:name="com.funbit.android" />
        <package android:name="com.WhatsApp3Plus" />
        <package android:name="com.instander.android" />
        <package android:name="com.smp.musicspeed" />
        <package android:name="com.imangi.templerun2" />
        <package android:name="in.moreretail.grocery" />
        <package android:name="com.jazarimusic.voloco" />
        <package android:name="fm.anchor.android" />
        <package android:name="com.dolby.dolby234" />
        <package android:name="in.startv.hotstar" />
        <package android:name="org.codeaurora.snapcam" />
        <package android:name="com.tibith.badboxing" />
        <package android:name="com.dsnl.multicall" />
        <package android:name="com.newlang.weelife" />
        <package android:name="bolo.codeplay.com.bolo" />
        <package android:name="com.miniclip.carrom" />
        <package android:name="com.daerisoft.thespikerm" />
        <package android:name="com.nekki.shadowfight" />
        <package android:name="com.realmestore.app" />
        <package android:name="com.afmobi.boomplayer" />
        <package android:name="com.DVloper.Granny3" />
        <package android:name="com.netease.g93na" />
        <package android:name="com.superking.ludo.star" />
        <package android:name="com.epicgames.fortnite" />
        <package android:name="com.ptitnightmare.wwv" />
        <package android:name="com.dvloper.granny" />
        <package android:name="com.facebook.games" />
        <package android:name="mobile.leakapk.launcher" />
        <package android:name="com.yyinedu.pigeon" />
        <package android:name="com.jmtec.magicsound" />
        <package android:name="com.lenovo.anyshare.gps" />
        <package android:name="com.pengyou.cloneapp" />
        <package android:name="com.noclip.multiplayer" />
        <package android:name="kwai.video.downloader" />
        <package android:name="com.netease.racerna" />
        <package android:name="com.ganhigh.calamansi" />
        <package android:name="com.rsg.heroesevolved" />
        <package android:name="com.jio.media.ondemand" />
        <package android:name="com.pz.life.android" />
        <package android:name="com.projz.zed.android" />
        <package android:name="com.lutech.voicechanger" />
        <package android:name="me.freecall.callglobal" />
        <package android:name="com.espn.score_center" />
        <package android:name="com.garena.game.bc" />
        <package android:name="com.azarlive.android" />
        <package android:name="com.ansangha.drdriving" />
        <package android:name="com.visnalize.win7simu" />
        <package android:name="com.game.friends.android" />
        <package android:name="video.like" />
        <package android:name="com.glovo" />
        <package android:name="com.wenext.wayak" />
        <package android:name="com.MadOut.BIG" />
        <package android:name="tv.ip.edusp" />
        <package android:name="com.happymod.apk" />
        <package android:name="io.walkietalkie" />
        <package android:name="com.anwhatsapp" />
        <package android:name="com.oplus.melody" />
        <package android:name="com.eterno" />
        <package android:name="org.jitsi.meet" />
        <package android:name="com.turkcell.bip" />
        <package android:name="com.arre.voice" />
        <package android:name="com.skype.raider" />
        <package android:name="org.videolan.vlc" />
        <package android:name="com.wheat.mango" />
        <package android:name="free.vpn.private" />
        <package android:name="com.zing.mp3" />
        <package android:name="com.oyetalk.tv" />
        <package android:name="com.miui.notes" />
        <package android:name="mini.video.chat" />
        <package android:name="com.kwai.video" />
        <package android:name="app.idous.voice" />
        <package android:name="com.haki.mobile" />
        <package android:name="com.oimvo.discdj" />
        <package android:name="com.bharatpe.app" />
        <package android:name="com.kakao.talk" />
        <package android:name="com.antsmediadua.dragonultraeditionwallpaper" />
        <package android:name="com.bg.pakistan.cricket.league" />
        <package android:name="com.lakshyamathematics.sdyadavmath" />
        <package android:name="com.cookiedevs.ciao.android" />
        <package android:name="com.PoxelStudios.DudeTheftAuto" />
        <package android:name="com.blitzteam.battleprime" />
        <package android:name="com.kurogame.gplay.punishing.grayraven.en" />
        <package android:name="com.monotype.android.font.kapp.custom_1801597010" />
        <package android:name="com.playstation.remoteplay" />
        <package android:name="air.com.shdgames.sierra7.gp" />
        <package android:name="com.appsamurai.maxgameturbo" />
        <package android:name="com.funtomic.matchmasters" />
        <package android:name="com.kapidhvaj.textrepeater" />
        <package android:name="com.google.android.apps.googleassistant" />
        <package android:name="com.trng.xuuyen.fakeconversationchat" />
        <package android:name="com.androidrocker.voicechanger" />
        <package android:name="com.watermelon.fruit.merge" />
        <package android:name="com.simplemobiletools.voicerecorder" />
        <package android:name="org.chromium.webapk.aaf925490d3e06189_v2" />
        <package android:name="inc.trilokia.pubgfxtool.free" />
        <package android:name="com.get.recharge.appli.recharge" />
        <package android:name="dj.music.djing.remix.song" />
        <package android:name="homeworkout.homeworkouts.noequipment" />
        <package android:name="com.sec.android.app.music" />
        <package android:name="com.ByteRaft.IndianBusSimulator" />
        <package android:name="com.createstickers.stickerwhatsapp" />
        <package android:name="org.chromium.webapk.af02dd1d9fd0048f5_v2" />
        <package android:name="com.outfit7.movingeye.swampattack" />
        <package android:name="photo.camera.beauty.hd.camera" />
        <package android:name="xyz.penpencil.physicswala" />
        <package android:name="voice.changer.auto.tune.editor.recorder" />
        <package android:name="com.callvoicechanger.voicecallchanger.voicechanger.girlcallvoicechanger" />
        <package android:name="com.hornet.android.huawei" />
        <package android:name="com.gamestar.pianoperfect" />
        <package android:name="com.free.talk.alh.global.im" />
        <package android:name="com.samsung.android.app.notes.addons" />
        <package android:name="mp3videoconverter.videotomp3converter.audioconverter" />
        <package android:name="com.scottgames.fivenightsatfreddys" />
        <package android:name="br.com.escolhatecnologia.vozdonarrador" />
        <package android:name="jawline.exercises.slim.face.yoga" />
        <package android:name="com.monotype.android.font.kapp.custom_1714429462" />
        <package android:name="com.voicechanger.funnyvoice.voiceeffects.femalevoice" />
        <package android:name="com.miniclip.cricketleague" />
        <package android:name="com.wallpaperscraft.wallpaper" />
        <package android:name="com.google.android.apps.recorder" />
        <package android:name="com.kikaoem.qisiemoji.inputmethod.cy.go" />
        <package android:name="com.minigames.sweetcrossing" />
        <package android:name="com.jatodoshackers.returnsffh4v119" />
        <package android:name="file.manager.classification.dir.tree.structure.ftp" />
        <package android:name="com.zippymob.games.brickbreaker" />
        <package android:name="com.robtopx.geometrydashsubzero" />
        <package android:name="com.silentservices.hushsms" />
        <package android:name="org.chromium.webapk.a585792c72cc66d77_v2" />
        <package android:name="com.softinit.iquitos.whatsweb" />
        <package android:name="com.techapps.fake.call.prank.free" />
        <package android:name="com.quran.labs.androidquran" />
        <package android:name="com.video.downloader.no.watermark.tiktok" />
        <package android:name="freecall.phone.free.call.wifi.calling" />
        <package android:name="air.com.ubisoft.brawl.halla.platform.fighting.action.pvp" />
        <package android:name="com.rainbow.block.geo.dash.rythm.action.platform.jump.games" />
        <package android:name="com.dsi.ant.plugins.antplus" />
        <package android:name="com.phoenix.xtrememotorist" />
        <package android:name="com.fiveszone.onetrueidcaller" />
        <package android:name="com.tothemoon.voice_record_and_changer" />
        <package android:name="com.google.android.apps.meetings" />
        <package android:name="com.barsstudios.swordplay" />
        <package android:name="com.imaginstudio.imagetools.pixellab" />
        <package android:name="com.brasilroleplaylauncher" />
        <package android:name="com.fungame.fakecall.prankfriend" />
        <package android:name="com.robtopx.geometrydashmeltdown" />
        <package android:name="com.clusterdev.malayalamkeyboard" />
        <package android:name="sixpack.sixpackabs.absworkout" />
        <package android:name="com.screenmirrorapp.casttotv.screenmirroring" />
        <package android:name="com.teachmint.teachmint_ni" />
        <package android:name="net.apex_designs.payback2" />
        <package android:name="com.kwai.kibt.livepartner.game" />
        <package android:name="com.roryhoppe.kristinejacobson.marquardt" />
        <package android:name="com.excelliance.multiaccount" />
        <package android:name="aichatbot.keyboard.translate.aiask.artgenerator" />
        <package android:name="com.swings.nearme.gamecenter" />
        <package android:name="live.bunch.group.video.chat.party.games" />
        <package android:name="com.tbegames.and.best_moto_race" />
        <package android:name="com.arkgames.ggplay.tlonglobal" />
        <package android:name="com.jayazone.screen.internal.audio.recorder" />
        <package android:name="com.gbox.com.facebook.orca" />
        <package android:name="jp.co.quadsystem.skyphone" />
        <package android:name="com.stickman.warriors.stickwarriors.dragon.shadow.fight" />
        <package android:name="com.valvesoftware.source.clientmod" />
        <package android:name="com.supereffect.voicechanger" />
        <package android:name="com.antivirus.mobilesecurity.viruscleaner.applock" />
        <package android:name="com.JindoBlu.TwoPlayerGamesChallenge" />
        <package android:name="com.google.android.apps.docs.editors.slides" />
        <package android:name="com.mventus.ncell.activity" />
        <package android:name="com.mi.android.go.globallauncher" />
        <package android:name="com.riotgames.league.teamfighttacticsvn" />
        <package android:name="com.catsbit.oxidesurvivalisland" />
        <package android:name="com.noorgames.basketbattle" />
        <package android:name="com.activision.callofduty.codpbt" />
        <package android:name="free.vpn.unblock.proxy.turbovpn.lite" />
        <package android:name="vidma.screenrecorder.videorecorder.videoeditor.pro" />
        <package android:name="com.blogspot.blakbin.adjustable" />
        <package android:name="com.monotype.android.font.kapp.custom_1345535786" />
        <package android:name="com.google.android.apps.chromecast.app" />
        <package android:name="com.google.android.apps.youtube.producer" />
        <package android:name="com.esharp.livemickaraoke" />
        <package android:name="com.drivezone.car.race.game" />
        <package android:name="com.wondershare.filmorago" />
        <package android:name="com.appsamurai.greenshark" />
        <package android:name="com.lyrebirdstudio.cartoon" />
        <package android:name="com.gravity.roo.sea.official" />
        <package android:name="com.mojang.minecraftpr.patch" />
        <package android:name="com.timewarp.scanface.selfiephotoeditor.videopopulartiktok" />
        <package android:name="com.andromo.dev391844.app455786" />
        <package android:name="net.bitplane.android.microphone" />
        <package android:name="com.andmi.SpeedRacing.free" />
        <package android:name="com.iexceed.appzillon.ippbMB" />
        <package android:name="com.google.android.apps.cameralite" />
        <package android:name="com.g19mobile.gamebooster" />
        <package android:name="com.google.android.accessibility.soundamplifier" />
        <package android:name="myrecorder.voicerecorder.voicememos.audiorecorder.recordingapp" />
        <package android:name="com.bigqsys.mobileprinter" />
        <package android:name="com.samsung.SMT.lang_hu_hu_f00" />
        <package android:name="com.gamelemo.rainbowcubes" />
        <package android:name="com.hidephoto.hidevideo.applock" />
        <package android:name="cn.huidutechnology.pubstar" />
        <package android:name="com.zapak.worldcup.t20.cricket" />
        <package android:name="com.basic.videodownloader.allmovie" />
        <package android:name="app.revanced.android.youtube" />
        <package android:name="com.apapps.voicechanger.voiceeffects.girlsvoicechanger.supervoiceeffects.free" />
        <package android:name="com.impossiblecarstunts.formula.cargames.megaramp.racinggames.stunt.cardriving" />
        <package android:name="com.screenrecorder.gamerecorder.livescreenrecorder" />
        <package android:name="com.google.android.apps.accessibility.voiceaccess" />
        <package android:name="com.samsung.android.perf_z" />
        <package android:name="com.duokan.phone.remotecontroller" />
        <package android:name="com.voicerecoder.starnest" />
        <package android:name="com.coloros.soundrecorder" />
        <package android:name="chatgpt.ai.chatbot.open.chat.gpt.bot.writer.assistant" />
        <package android:name="com.intermedia.hd.camera.professional" />
        <package android:name="com.nautilus.RealCricket3D" />
        <package android:name="com.fooview.android.fooview" />
        <package android:name="in.gov.uidai.mAadhaarPlus" />
        <package android:name="com.dts.freefireth.huawei" />
        <package android:name="fonts.keyboard.fontboard.stylish" />
        <package android:name="com.brilliant.connect.com.bd" />
        <package android:name="note.notesapp.notebook.notepad.stickynotes.colornote" />
        <package android:name="com.dogbytegames.offtheroad" />
        <package android:name="co.speechnotes.speechnotes" />
        <package android:name="com.ovilex.eurotruckdriver" />
        <package android:name="com.tiltingpoint.spongebob" />
        <package android:name="com.rapps.funny.girls.voiceeffects.voice.changer.free" />
        <package android:name="br.com.rodrigokolb.realguitar" />
        <package android:name="com.fundeasy.bankbalancecheck" />
        <package android:name="com.dsi.ant.service.socket" />
        <package android:name="com.TwentySeven.MelonPlayground" />
        <package android:name="com.whereismytrain.android" />
        <package android:name="com.kimcy929.screenrecorder" />
        <package android:name="com.gbox.com.facebook.lite" />
        <package android:name="com.samsung.android.game.gameboosterplus" />
        <package android:name="com.chillingo.robberybob2.android.gplay" />
        <package android:name="com.banix.voiceeffect.voicechanger" />
        <package android:name="com.google.android.apps.googlevoice" />
        <package android:name="com.infinityidea.thirdeye" />
        <package android:name="com.audio.voicechanger.music.editor" />
        <package android:name="com.android.bbk.lockscreen3" />
        <package android:name="videoeditor.videomaker.videoeditorforyoutube" />
        <package android:name="com.oneplus.soundrecorder" />
        <package android:name="com.legendmame.dinoemulator" />
        <package android:name="com.sofarsogood.incredibox" />
        <package android:name="com.simplemobiletools.dialer" />
        <package android:name="com.dualspace.multispace.android" />
        <package android:name="com.gps.speedometer.stepcounter.odometer.speedtracker" />
        <package android:name="org.dashnet.cookieclicker" />
        <package android:name="com.gbox.com.google.android.apps.classroom" />
        <package android:name="org.chromium.webapk.a3c1f56a59f63266f_v2" />
        <package android:name="codematics.universal.tv.remote.control" />
        <package android:name="com.chillingo.incrediblejack.android.rowgplay1" />
        <package android:name="com.google.android.googlequicksearchbox" />
        <package android:name="music.musicplayer.mp3player.musicapps.musicdownloader" />
        <package android:name="com.tianxingjian.supersound" />
        <package android:name="org.chromium.webapk.a775c19d686b08350_v2" />
        <package android:name="ro.alynsampmobile.launcher" />
        <package android:name="com.bleacherreport.android.teamstream" />
        <package android:name="com.tedrasoft.music.maker" />
        <package android:name="com.google.android.apps.nbu.paisa.user" />
        <package android:name="any.call.international.phone.wifi.calling" />
        <package android:name="com.rdrift.nearme.gamecenter" />
        <package android:name="com.samsung.android.soundassistant" />
        <package android:name="com.google.android.apps.docs.editors.sheets" />
        <package android:name="com.keplerians.icescreamunited" />
        <package android:name="com.ikeyboard.theme.glass.water.drops" />
        <package android:name="com.zipoapps.voice.recorder.memos" />
        <package android:name="com.PlayMax.playergames" />
        <package android:name="com.bKash.customerapp" />
        <package android:name="com.app.dream11Pro" />
        <package android:name="com.crazygames.cloudwave" />
        <package android:name="global.duovoice.android" />
        <package android:name="ro.alyn_sampmobile.game" />
        <package android:name="com.sandhill.yochat" />
        <package android:name="com.gbox.com.discord" />
        <package android:name="com.instagram.barcelona" />
        <package android:name="com.polygon.arena" />
        <package android:name="ai.naam.android.app" />
        <package android:name="com.satszb.saty.gamerbox" />
        <package android:name="com.gravity.roo.sea" />
        <package android:name="com.netease.sheltergp" />
        <package android:name="com.secugen.rdservice" />
        <package android:name="com.Dani.MilkmanKarlson" />
        <package android:name="com.firsttouchgames.smp" />
        <package android:name="com.amberit.dialer" />
        <package android:name="com.facemoji.lite.xiaomi" />
        <package android:name="com.xiaomi.scanner" />
        <package android:name="com.zappcues.gamingmode" />
        <package android:name="com.day.callme.lite" />
        <package android:name="com.sandboxol.blockymods" />
        <package android:name="com.taxsee.taxsee" />
        <package android:name="ir.ilmili.telegraph" />
        <package android:name="com.lilithgame.roc.gp" />
        <package android:name="com.skgames.trafficrider" />
        <package android:name="com.blued.international" />
        <package android:name="co.yellw.yellowapp" />
        <package android:name="online.whatsticker" />
        <package android:name="com.fontskeyboard.fonts" />
        <package android:name="com.dianyun.chikii" />
        <package android:name="com.soundcloud.android" />
        <package android:name="com.yowhats.sofitab" />
        <package android:name="com.FurqonhkApp.McKevin" />
        <package android:name="com.instapro2.android" />
        <package android:name="com.movile.playkids.pkxd" />
        <package android:name="com.magicfluids.demo" />
        <package android:name="com.brave.browser" />
        <package android:name="com.ction.playergames" />
        <package android:name="com.tencent.igxiaomi" />
        <package android:name="cz.gdmt.AnnelidsDemo" />
        <package android:name="com.goodwy.dialer" />
        <package android:name="media.music.musicplayer" />
        <package android:name="sg.bigo.ludolegend" />
        <package android:name="com.transsion.phoenix" />
        <package android:name="misplayer.games.archery" />
        <package android:name="com.gartic.Gartic" />
        <package android:name="com.instapro.android" />
        <package android:name="com.crrepa.band.dafit" />
        <package android:name="com.smule.singandroid" />
        <package android:name="com.linkedin.android" />
        <package android:name="com.meesho.supply" />
        <package android:name="net.zedge.android" />
        <package android:name="app.source.getcontact" />
        <package android:name="com.qltech.whatsweb" />
        <package android:name="com.playstrom.dop4" />
        <package android:name="com.axlebolt.standoff2" />
        <package android:name="com.outfit7.talkingtom" />
        <package android:name="com.textmeinc.textme" />
        <package android:name="com.critical.strike2" />
        <package android:name="com.begma.bhoppro" />
        <package android:name="com.mantra.rdservice" />
        <package android:name="com.duckprog.makhos" />
        <package android:name="cn.xiaofengkj.fitpro" />
        <package android:name="com.YoStar.AetherGazer" />
        <package android:name="com.nextwave.wcc2" />
        <package android:name="io.hexman.xiconchanger" />
        <package android:name="com.vng.playtogether" />
        <package android:name="me.talkyou.app.im" />
        <package android:name="com.nvidia.geforcenow" />
        <package android:name="com.wemesh.android" />
        <package android:name="com.miui.weather2" />
        <package android:name="com.playrix.township" />
        <package android:name="com.gbox.com.whatsapp" />
        <package android:name="launcher.morgan.mobile" />
        <package android:name="com.facebook.litn" />
        <package android:name="com.wachi.hd.recorder" />
        <package android:name="io.appium.settings" />
        <package android:name="com.ai.chat.bot.aichat" />
        <package android:name="com.bg.cricket.worldcup" />
        <package android:name="com.netease.racernathird" />
        <package android:name="plus.ride.huluchat" />
        <package android:name="world.voya.android" />
        <package android:name="com.lethe.petrolhead" />
        <package android:name="inc.trilokia.pubgfxtool" />
        <package android:name="me.onenrico.animeindo" />
        <package android:name="com.archosaur.vn.dr.gp" />
        <package android:name="com.video.fun.app" />
        <package android:name="com.moonton.mobilehero" />
        <package android:name="com.ylhy.evoos.and" />
        <package android:name="io.mrarm.mctoolbox" />
        <package android:name="com.cmplay.tiles2" />
        <package android:name="com.wphotopia_9508791" />
        <package android:name="com.bng.magiccall" />
        <package android:name="com.ct.shots.video" />
        <package android:name="com.opera.branding.news" />
        <package android:name="com.bnesim.android" />
        <package android:name="com.projz.z.android" />
        <package android:name="com.vkontakte.android" />
        <package android:name="com.ludashi.superboost" />
        <package android:name="com.tallteam.citychase" />
        <package android:name="com.smmservice.printer" />
        <package android:name="com.kingsgroup.sos" />
        <package android:name="com.ludashi.dualspace" />
        <package android:name="com.microsoft.bingintl" />
        <package android:name="com.twitter.android.lite" />
        <package android:name="com.shadow.blackhole" />
        <package android:name="com.NewAsia.followers" />
        <package android:name="com.imvu.mobilecordova" />
        <package android:name="com.android.MGC_8_9_097" />
        <package android:name="com.opera.mini.native" />
        <package android:name="bubbleshooter.orig" />
        <package android:name="com.kiwibrowser.browser" />
        <package android:name="com.myntra.android" />
        <package android:name="com.pure.indosat.care" />
        <package android:name="com.noxinfinity.modskin" />
        <package android:name="com.netmarble.sk2gb" />
        <package android:name="com.mobile.ai.chatgpt" />
        <package android:name="ru.rtpczlsd.huflrvhqv" />
        <package android:name="ro.samp_mobile.launcher" />
        <package android:name="com.immortal.chaos.gp" />
        <package android:name="com.clubhouse.app" />
        <package android:name="com.msf.kbank.mobile" />
        <package android:name="com.sec.kidsplat.phone" />
        <package android:name="com.jio.jioplay.tv" />
        <package android:name="me.ninjagram.messenger" />
        <package android:name="com.cdg.tictactoe" />
        <package android:name="com.microsoft.skydrive" />
        <package android:name="com.mi.global.shop" />
        <package android:name="com.eliferun.music" />
        <package android:name="com.guidedways.iQuran" />
        <package android:name="com.pointone.buddyglobal" />
        <package android:name="com.greentech.quran" />
        <package android:name="sensilagff.nickshockytb" />
        <package android:name="com.UCMobile.intl" />
        <package android:name="com.solou.catendless.run" />
        <package android:name="com.instaplus.android" />
        <package android:name="com.voicechat.live.group" />
        <package android:name="chat.ometv.dating" />
        <package android:name="com.denchi.vtubestudio" />
        <package android:name="mobile.morgan.plus" />
        <package android:name="e.books.reading.apps" />
        <package android:name="com.gai.status.saver.ssw" />
        <package android:name="com.islamdev.animetv" />
        <package android:name="com.baidu.thrgame" />
        <package android:name="com.github.kr328.clash" />
        <package android:name="com.rioo.runnersubway" />
        <package android:name="com.gameram.gameram" />
        <package android:name="com.miui.calculator" />
        <package android:name="com.easytalk.call" />
        <package android:name="com.andrwq.recorder" />
        <package android:name="com.rtsoft.growtopia" />
        <package android:name="sg.partying.android.lite" />
        <package android:name="com.vocalremover.unmix" />
        <package android:name="com.gheblenama2.amitis" />
        <package android:name="com.android.email" />
        <package android:name="com.ztnstudio.notepad" />
        <package android:name="com.g4m3studio.apk" />
        <package android:name="com.XJWL.SSmetaverse" />
        <package android:name="com.cbt.exam.browser" />
        <package android:name="com.netease.heatup" />
        <package android:name="videomaker.music.mv" />
        <package android:name="com.coloros.oppopods" />
        <package android:name="com.daaw.avee.lite" />
        <package android:name="com.mobzapp.voicefx" />
        <package android:name="com.redfinger.app" />
        <package android:name="com.rockstargames.gtavc" />
        <package android:name="com.kcstream.cing" />
        <package android:name="com.disney.disneyplus" />
        <package android:name="com.michatapp.im.lite" />
        <package android:name="com.pixonic.wwr" />
        <package android:name="com.heytap.music" />
        <package android:name="com.tinder" />
        <package android:name="cm.aptoide.pt" />
        <package android:name="com.play.chat" />
        <package android:name="com.blueWAplus" />
        <package android:name="com.telos.app.im" />
        <package android:name="com.play.rosea" />
        <package android:name="com.streamlabs" />
        <package android:name="com.sec.spp.push" />
        <package android:name="com.whatsappplus" />
        <package android:name="com.whatsapp.w4y" />
        <package android:name="com.razor.lite" />
        <package android:name="com.os.docvault" />
        <package android:name="com.halamate.app" />
        <package android:name="me.pou.app" />
        <package android:name="com.alaap.app" />
        <package android:name="com.panda.mouse" />
        <package android:name="com.joymi.seven" />
        <package android:name="com.ob3whatsapp" />
        <package android:name="com.my.defense" />
        <package android:name="com.nemo.vidmate" />
        <package android:name="com.obwhatsapp" />
        <package android:name="com.vng.mlbbvn" />
        <package android:name="com.yy.hiyo" />
        <package android:name="com.erwhatsapp" />
        <package android:name="brutal.strike.a7" />
        <package android:name="io.faceapp" />
        <package android:name="com.musixmusicx" />
        <package android:name="io.chingari.app" />
        <package android:name="com.rekoo.pubgm" />
        <package android:name="com.aim.racing" />
        <package android:name="cc.honista.app" />
        <package android:name="ru.iiec.pydroid3" />
        <package android:name="com.miui.compass" />
        <package android:name="net.one97.paytm" />
        <package android:name="nu.bi.moya" />
        <package android:name="com.wephoneapp" />
        <package android:name="com.bstar.intl" />
        <package android:name="com.kiwhatsapp" />
        <package android:name="com.quwan.ttchat" />
        <package android:name="com.haflla.soulu" />
        <package android:name="kik.android" />
        <package android:name="com.chrome.beta" />
        <package android:name="com.yy.biu" />
        <package android:name="com.ha2whatsapp" />
        <package android:name="com.aewhatsapp" />
        <package android:name="com.craftsman.go" />
        <package android:name="ir.medu.shad" />
        <package android:name="com.heyophone" />
        <package android:name="com.oplus.games" />
        <package android:name="com.booking" />
        <package android:name="com.pinterest" />
        <package android:name="com.loudtalks" />
        <package android:name="com.meitu.wink" />
        <package android:name="io.anonchat" />
        <package android:name="com.live.party" />
        <package android:name="com.beint.zangi" />
        <package android:name="com.ad2whatsapp" />
        <package android:name="town.pony.game" />
        <package android:name="io.winzo.games" />
        <package android:name="com.adobe.reader" />
        <package android:name="com.ihappydate" />
        <package android:name="com.interfun.buz" />
        <package android:name="com.beint.zangk" />
        <package android:name="com.gbwhatsapp2" />
        <package android:name="com.kk.chimpoon" />
        <package android:name="com.getstan" />
        <package android:name="org.xbet.client1" />
        <package android:name="com.prequel.app" />
        <package android:name="com.live.hey" />
        <package android:name="com.ag2whatsapp" />
        <package android:name="com.mpokket.app" />
        <package android:name="com.shujiu.Zaky" />
        <package android:name="com.rechx" />
        <package android:name="com.aliucord" />
        <package android:name="com.jio.jiogames" />
        <package android:name="com.br.top" />
        <package android:name="com.androvid" />
        <package android:name="com.who.tracky" />
        <package android:name="com.poe.android" />
        <package android:name="com.lmr.lfm" />
        <package android:name="cn.gem.android" />
        <package android:name="com.yo" />
        <package android:name="com.gb" />
        <package android:name="id.dana" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
        </intent>
        <intent>
            <action android:name="com.applovin.am.intent.action.APPHUB_SERVICE" />
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <permission android:name="com.abox.apps.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" android:protectionLevel="signature" />
    <uses-permission android:name="com.abox.apps.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="android.permission.READ_APP_SPECIFIC_LOCALES" />
    <uses-permission android:name="android.permission.ACCOUNT_MANAGER" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
    <uses-permission android:name="android.permission.TRANSMIT_IR" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.USE_SIP" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_PROFILE" />
    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission-sdk-23 android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" />
    <uses-permission android:name="com.samsung.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.samsung.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.motorola.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.motorola.launcher3.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.google.android.apps.nexuslauncher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.google.android.apps.nexuslauncher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="android.permission.READ_NETWORK_USAGE_HISTORY" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_USE_DATA_IN_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.ACCEPT_HANDOVER" />
    <uses-permission android:name="android.permission.NFC_TRANSACTION_EVENT" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.CALL_COMPANION_APP" />
    <uses-permission android:name="android.permission.REQUEST_PASSWORD_COMPLEXITY" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="com.google.android.c2dm.permission.SEND" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
    <application android:theme="@style/Theme_ABox" android:label="@string/app_name" android:icon="@mipmap/ic_launcher" android:name=".MainApplication" android:allowBackup="true" android:debuggable="true" android:extractNativeLibs="true" android:dataExtractionRules="@xml/backup_rules" android:fullBackupContent="true" android:roundIcon="@mipmap/ic_launcher_round" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:requestLegacyExternalStorage="true" android:preserveLegacyExternalStorage="false" android:hardwareAccelerated="off" android:fullBackupOnly="@xml/data_extraction_rules">
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" />
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token" />
        <activity android:theme="@style/com_facebook_activity_theme" android:label="@string/app_name" android:name="com.facebook.FacebookActivity" android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize" />
        <activity android:name="com.facebook.CustomTabActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="fbconnect" android:host="cct.com.abox.apps" />
            </intent-filter>
        </activity>
        <activity android:name=".activitys.LoginGuideActivity" android:exported="false" />
        <activity android:name=".activitys.FreeTryActivity" android:exported="false" />
        <activity android:theme="@style/AppTheme.Transparent" android:name=".activitys.RecommendAppDialogActivity" android:exported="false" android:process="@string/vlite_server_process_name" android:taskAffinity="com.abox.recommed_app.task" android:multiprocess="true" android:launchMode="singleTask" />
        <activity android:name=".activitys.RecommendActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.VipCenterActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.OrderHistoryActivity" android:exported="false" android:screenOrientation="portrait" />
        <service android:name=".components.LiteKeepAliveService" android:process=":voice_process" />
        <meta-data android:name="applovin.sdk.key" android:value="2anBvnTr1xLy6yVgiTnZDvr46avV8IgDLmylGGcaTTaUOmXqT36nxc5ypGLUHBEcZZPAsrCOciMAcue6ulE8QK" />
        <activity android:name=".activitys.LockManagerActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.AboutActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.RecordsActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.SettingsActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.VoiceExperienceActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.FeedbackActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:name=".activitys.AcquirePermissionGuideActivity" android:exported="false" android:screenOrientation="portrait" />
        <activity android:theme="@style/Theme_Translucent" android:name=".activitys.PayActivity" android:exported="true" android:launchMode="singleTop" android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="com.abox.apps.webpay" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity android:theme="@style/Theme_ABox_Transparent" android:name=".activitys.LauncherAppActivity" android:exported="true" android:taskAffinity=".AppLaunch" android:multiprocess="true" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize">
            <intent-filter>
                <action android:name="com.abox.android.action.LAUNCH_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:theme="@style/Theme_ABox_Transparent" android:name=".activitys.CloneAndLauncherAppActivity" android:exported="true" android:taskAffinity=".AppLaunch" android:multiprocess="true" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize">
            <intent-filter>
                <action android:name="com.abox.android.action.CLONE_LAUNCH_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:theme="@style/Theme_ABox_Splash" android:name=".activitys.SplashActivity" android:exported="true" android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".activitys.UnlockActivity" android:exported="false" android:screenOrientation="portrait" android:theme="@style/Theme_ABox_Splash" />
        <activity android:name=".activitys.MainActivity" android:exported="true" android:launchMode="singleTask" android:screenOrientation="portrait" />
        <activity android:name=".activitys.CloneActivity" android:exported="false" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize" android:windowSoftInputMode="adjustNothing" />
        <activity android:name=".activitys.LoginActivity" android:exported="false" android:launchMode="singleTop" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize" android:windowSoftInputMode="adjustNothing" />
        <activity android:name=".activitys.RegisterActivity" android:exported="false" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize" android:windowSoftInputMode="adjustNothing" />
        <activity android:name=".activitys.ForgotPwdActivity" android:exported="false" android:screenOrientation="portrait" android:configChanges="keyboardHidden|orientation|screenSize" android:windowSoftInputMode="adjustNothing" />
        <provider android:name="androidx.core.content.FileProvider" android:exported="false" android:authorities="com.abox.apps.fileprovider" android:grantUriPermissions="true">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths" />
        </provider>
        <provider android:name=".provider.CheckEnvProvider" android:permission="com.abox.android.mg.checkenv" android:exported="false" android:process="@string/vlite_server_process_name" android:authorities="com.abox.apps.check_env" />
        <meta-data android:name="sub-channel" android:value="google" />
        <activity android:theme="@style/PYPLAppTheme" android:name="com.paypal.pyplcheckout.ui.feature.home.activities.PYPLHomeActivity" android:exported="false" android:taskAffinity="com.pyplcheckout.task" android:launchMode="singleTop" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize" />
        <activity android:theme="@style/PYPLAppFullScreenTheme" android:name="com.paypal.pyplcheckout.ui.feature.home.activities.PYPLInitiateCheckoutActivity" android:exported="true" android:launchMode="singleTask" android:screenOrientation="portrait">
            <intent-filter android:autoVerify="true">
                <data android:scheme="com.abox.apps" android:host="paypalxo" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity android:theme="@style/PYPLAppTheme" android:name="com.paypal.openid.RedirectUriReceiverActivity" android:exported="true" android:excludeFromRecents="true" android:screenOrientation="portrait">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="com.abox.apps" android:host="paypalpay" />
            </intent-filter>
        </activity>
        <activity android:theme="@style/PYPLAppTheme" android:name="com.paypal.pyplcheckout.ui.feature.threeds.ThreeDS20Activity" android:exported="false" android:taskAffinity="com.pyplcheckout.threeds" android:launchMode="singleTop" android:screenOrientation="portrait" />
        <activity android:name="com.paypal.authcore.authentication.TokenActivity" />
        <activity android:theme="@android:style/Theme.Translucent.NoTitleBar" android:name="com.paypal.openid.AuthorizationManagementActivity" android:exported="false" android:launchMode="singleTask" />
        <meta-data android:name="com.google.android.play.billingclient.version" android:value="6.1.0" />
        <activity android:theme="@android:style/Theme.Translucent.NoTitleBar" android:name="com.android.billingclient.api.ProxyBillingActivity" android:exported="false" android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize" />
        <activity android:theme="@android:style/Theme.Translucent.NoTitleBar" android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity" android:exported="false" android:excludeFromRecents="true" />
        <service android:name="com.google.android.gms.auth.api.signin.RevocationBoundService" android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION" android:exported="true" android:visibleToInstantApps="true" />
        <provider android:name="com.applovin.sdk.AppLovinInitProvider" android:exported="false" android:authorities="com.abox.apps.applovininitprovider" android:initOrder="101" />
        <activity android:name="com.applovin.adview.AppLovinFullscreenActivity" android:exported="false" android:launchMode="singleTop" android:taskAffinity="behind" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:hardwareAccelerated="true" />
        <activity android:theme="@android:style/Theme.NoTitleBar.Fullscreen" android:name="com.applovin.adview.AppLovinFullscreenThemedActivity" android:exported="false" android:launchMode="singleTop" android:taskAffinity="behind" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:hardwareAccelerated="true" />
        <activity android:name="com.applovin.sdk.AppLovinWebViewActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@android:style/Theme.NoTitleBar.Fullscreen" android:name="com.applovin.mediation.hybridAds.MaxHybridMRecAdActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@android:style/Theme.NoTitleBar.Fullscreen" android:name="com.applovin.mediation.hybridAds.MaxHybridNativeAdActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerDetailActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerMultiAdActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerAdUnitsListActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerAdUnitDetailActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerTcfInfoListActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerTcfStringActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerTcfVendorDetailListActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerTestModeNetworkActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" android:name="com.applovin.mediation.MaxDebuggerWaterfallKeywordsActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" android:name="com.applovin.creative.MaxCreativeDebuggerActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <activity android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" android:name="com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity" android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" />
        <service android:name="com.applovin.impl.adview.activity.FullscreenAdService" android:exported="false" android:enabled="false" />
        <activity android:theme="@android:style/Theme.Translucent.NoTitleBar" android:name="com.google.android.gms.common.api.GoogleApiActivity" android:exported="false" />
        <meta-data android:name="android.notch_support" android:value="true" />
        <activity android:theme="@android:style/Theme.Translucent.NoTitleBar" android:name="com.lxj.xpopup.util.XPermission$PermissionActivity" />
        <provider android:name="com.abox.audiotransform.provider.VoiceApiProvider" android:exported="true" android:process=":voice_process" android:authorities="com.abox.audiotransfer.api" />
        <activity android:name="com.facebook.CustomTabMainActivity" />
        <service android:name="com.google.firebase.components.ComponentDiscoveryService" android:exported="false" android:directBootAware="true">
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar" android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>
        <provider android:name="com.google.firebase.provider.FirebaseInitProvider" android:exported="false" android:authorities="com.abox.apps.firebaseinitprovider" android:initOrder="100" android:directBootAware="true" />
        <receiver android:name="com.google.android.gms.measurement.AppMeasurementReceiver" android:enabled="true" android:exported="false" />
        <service android:name="com.google.android.gms.measurement.AppMeasurementService" android:enabled="true" android:exported="false" />
        <service android:name="com.google.android.gms.measurement.AppMeasurementJobService" android:permission="android.permission.BIND_JOB_SERVICE" android:enabled="true" android:exported="false" />
        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version" />
        <provider android:name="com.facebook.internal.FacebookInitProvider" android:exported="false" android:authorities="com.abox.apps.FacebookInitProvider" />
        <receiver android:name="o.ServiceConnectionLeakedViolation" android:exported="false">
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver android:name="o.UnbufferedIoViolation$TaskDescription" android:exported="false">
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
            </intent-filter>
        </receiver>
        <provider android:name="androidx.startup.InitializationProvider" android:exported="false" android:authorities="com.abox.apps.androidx-startup">
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup" />
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup" />
            <meta-data android:name="androidx.profileinstaller.ProfileInstallerInitializer" android:value="androidx.startup" />
        </provider>
        <provider android:name="com.lzf.easyfloat.EasyFloatInitializer" android:exported="false" android:enabled="true" android:authorities="com.abox.apps.EasyFloatInitializer" />
        <provider android:name="com.squareup.picasso.PicassoProvider" android:exported="false" android:authorities="com.abox.apps.com.squareup.picasso" />
        <service android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery" android:exported="false">
            <meta-data android:name="backend:com.google.android.datatransport.cct.CctBackendFactory" android:value="cct" />
        </service>
        <provider android:name="com.liulishuo.okdownload.OkDownloadProvider" android:exported="false" android:authorities="com.abox.apps.com.liulishuo.okdownload" />
        <service android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService" android:permission="android.permission.BIND_JOB_SERVICE" android:exported="false" />
        <receiver android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver" android:exported="false" />
        <receiver android:name="androidx.profileinstaller.ProfileInstallReceiver" android:permission="android.permission.DUMP" android:directBootAware="true" android:exported="true" android:enabled="false">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
        <activity android:="@android:style/Theme.Translucent.NoTitleBar" android:name="com.vlite.sdk.proxy.ShortcutForwardActivity" android:exported="true" android:="com.vlite.proxy.shortcut" android:="true">
            <intent-filter>
                <action android:name="vlite.intent.action.SHORTCUT_FORWARD" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape01" android:exported="false" android:=":vlapp01" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider01" android:exported="false" android:=":vlapp01" android:="com.abox.apps.vlite.stub.provider01" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service01" android:exported="false" android:=":vlapp01" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService01" android:exported="false" android:=":vlapp01" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape02" android:exported="false" android:=":vlapp02" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider02" android:exported="false" android:=":vlapp02" android:="com.abox.apps.vlite.stub.provider02" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service02" android:exported="false" android:=":vlapp02" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService02" android:exported="false" android:=":vlapp02" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape03" android:exported="false" android:=":vlapp03" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider03" android:exported="false" android:=":vlapp03" android:="com.abox.apps.vlite.stub.provider03" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service03" android:exported="false" android:=":vlapp03" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService03" android:exported="false" android:=":vlapp03" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape04" android:exported="false" android:=":vlapp04" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider04" android:exported="false" android:=":vlapp04" android:="com.abox.apps.vlite.stub.provider04" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service04" android:exported="false" android:=":vlapp04" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService04" android:exported="false" android:=":vlapp04" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape05" android:exported="false" android:=":vlapp05" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider05" android:exported="false" android:=":vlapp05" android:="com.abox.apps.vlite.stub.provider05" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service05" android:exported="false" android:=":vlapp05" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService05" android:exported="false" android:=":vlapp05" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape06" android:exported="false" android:=":vlapp06" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider06" android:exported="false" android:=":vlapp06" android:="com.abox.apps.vlite.stub.provider06" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service06" android:exported="false" android:=":vlapp06" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService06" android:exported="false" android:=":vlapp06" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape07" android:exported="false" android:=":vlapp07" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider07" android:exported="false" android:=":vlapp07" android:="com.abox.apps.vlite.stub.provider07" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service07" android:exported="false" android:=":vlapp07" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService07" android:exported="false" android:=":vlapp07" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape08" android:exported="false" android:=":vlapp08" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider08" android:exported="false" android:=":vlapp08" android:="com.abox.apps.vlite.stub.provider08" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service08" android:exported="false" android:=":vlapp08" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService08" android:exported="false" android:=":vlapp08" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape09" android:exported="false" android:=":vlapp09" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider09" android:exported="false" android:=":vlapp09" android:="com.abox.apps.vlite.stub.provider09" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service09" android:exported="false" android:=":vlapp09" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService09" android:exported="false" android:=":vlapp09" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0a" android:exported="false" android:=":vlapp0a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0a" android:exported="false" android:=":vlapp0a" android:="com.abox.apps.vlite.stub.provider0a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0a" android:exported="false" android:=":vlapp0a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0a" android:exported="false" android:=":vlapp0a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0b" android:exported="false" android:=":vlapp0b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0b" android:exported="false" android:=":vlapp0b" android:="com.abox.apps.vlite.stub.provider0b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0b" android:exported="false" android:=":vlapp0b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0b" android:exported="false" android:=":vlapp0b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0c" android:exported="false" android:=":vlapp0c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0c" android:exported="false" android:=":vlapp0c" android:="com.abox.apps.vlite.stub.provider0c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0c" android:exported="false" android:=":vlapp0c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0c" android:exported="false" android:=":vlapp0c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0d" android:exported="false" android:=":vlapp0d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0d" android:exported="false" android:=":vlapp0d" android:="com.abox.apps.vlite.stub.provider0d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0d" android:exported="false" android:=":vlapp0d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0d" android:exported="false" android:=":vlapp0d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0e" android:exported="false" android:=":vlapp0e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0e" android:exported="false" android:=":vlapp0e" android:="com.abox.apps.vlite.stub.provider0e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0e" android:exported="false" android:=":vlapp0e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0e" android:exported="false" android:=":vlapp0e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape0f" android:exported="false" android:=":vlapp0f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider0f" android:exported="false" android:=":vlapp0f" android:="com.abox.apps.vlite.stub.provider0f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service0f" android:exported="false" android:=":vlapp0f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService0f" android:exported="false" android:=":vlapp0f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape10" android:exported="false" android:=":vlapp10" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider10" android:exported="false" android:=":vlapp10" android:="com.abox.apps.vlite.stub.provider10" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service10" android:exported="false" android:=":vlapp10" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService10" android:exported="false" android:=":vlapp10" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape11" android:exported="false" android:=":vlapp11" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider11" android:exported="false" android:=":vlapp11" android:="com.abox.apps.vlite.stub.provider11" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service11" android:exported="false" android:=":vlapp11" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService11" android:exported="false" android:=":vlapp11" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape12" android:exported="false" android:=":vlapp12" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider12" android:exported="false" android:=":vlapp12" android:="com.abox.apps.vlite.stub.provider12" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service12" android:exported="false" android:=":vlapp12" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService12" android:exported="false" android:=":vlapp12" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape13" android:exported="false" android:=":vlapp13" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider13" android:exported="false" android:=":vlapp13" android:="com.abox.apps.vlite.stub.provider13" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service13" android:exported="false" android:=":vlapp13" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService13" android:exported="false" android:=":vlapp13" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape14" android:exported="false" android:=":vlapp14" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider14" android:exported="false" android:=":vlapp14" android:="com.abox.apps.vlite.stub.provider14" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service14" android:exported="false" android:=":vlapp14" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService14" android:exported="false" android:=":vlapp14" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape15" android:exported="false" android:=":vlapp15" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider15" android:exported="false" android:=":vlapp15" android:="com.abox.apps.vlite.stub.provider15" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service15" android:exported="false" android:=":vlapp15" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService15" android:exported="false" android:=":vlapp15" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape16" android:exported="false" android:=":vlapp16" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider16" android:exported="false" android:=":vlapp16" android:="com.abox.apps.vlite.stub.provider16" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service16" android:exported="false" android:=":vlapp16" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService16" android:exported="false" android:=":vlapp16" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape17" android:exported="false" android:=":vlapp17" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider17" android:exported="false" android:=":vlapp17" android:="com.abox.apps.vlite.stub.provider17" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service17" android:exported="false" android:=":vlapp17" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService17" android:exported="false" android:=":vlapp17" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape18" android:exported="false" android:=":vlapp18" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider18" android:exported="false" android:=":vlapp18" android:="com.abox.apps.vlite.stub.provider18" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service18" android:exported="false" android:=":vlapp18" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService18" android:exported="false" android:=":vlapp18" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape19" android:exported="false" android:=":vlapp19" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider19" android:exported="false" android:=":vlapp19" android:="com.abox.apps.vlite.stub.provider19" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service19" android:exported="false" android:=":vlapp19" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService19" android:exported="false" android:=":vlapp19" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1a" android:exported="false" android:=":vlapp1a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1a" android:exported="false" android:=":vlapp1a" android:="com.abox.apps.vlite.stub.provider1a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1a" android:exported="false" android:=":vlapp1a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1a" android:exported="false" android:=":vlapp1a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1b" android:exported="false" android:=":vlapp1b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1b" android:exported="false" android:=":vlapp1b" android:="com.abox.apps.vlite.stub.provider1b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1b" android:exported="false" android:=":vlapp1b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1b" android:exported="false" android:=":vlapp1b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1c" android:exported="false" android:=":vlapp1c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1c" android:exported="false" android:=":vlapp1c" android:="com.abox.apps.vlite.stub.provider1c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1c" android:exported="false" android:=":vlapp1c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1c" android:exported="false" android:=":vlapp1c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1d" android:exported="false" android:=":vlapp1d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1d" android:exported="false" android:=":vlapp1d" android:="com.abox.apps.vlite.stub.provider1d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1d" android:exported="false" android:=":vlapp1d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1d" android:exported="false" android:=":vlapp1d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1e" android:exported="false" android:=":vlapp1e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1e" android:exported="false" android:=":vlapp1e" android:="com.abox.apps.vlite.stub.provider1e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1e" android:exported="false" android:=":vlapp1e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1e" android:exported="false" android:=":vlapp1e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape1f" android:exported="false" android:=":vlapp1f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider1f" android:exported="false" android:=":vlapp1f" android:="com.abox.apps.vlite.stub.provider1f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service1f" android:exported="false" android:=":vlapp1f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService1f" android:exported="false" android:=":vlapp1f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape20" android:exported="false" android:=":vlapp20" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider20" android:exported="false" android:=":vlapp20" android:="com.abox.apps.vlite.stub.provider20" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service20" android:exported="false" android:=":vlapp20" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService20" android:exported="false" android:=":vlapp20" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape21" android:exported="false" android:=":vlapp21" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider21" android:exported="false" android:=":vlapp21" android:="com.abox.apps.vlite.stub.provider21" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service21" android:exported="false" android:=":vlapp21" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService21" android:exported="false" android:=":vlapp21" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape22" android:exported="false" android:=":vlapp22" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider22" android:exported="false" android:=":vlapp22" android:="com.abox.apps.vlite.stub.provider22" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service22" android:exported="false" android:=":vlapp22" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService22" android:exported="false" android:=":vlapp22" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape23" android:exported="false" android:=":vlapp23" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider23" android:exported="false" android:=":vlapp23" android:="com.abox.apps.vlite.stub.provider23" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service23" android:exported="false" android:=":vlapp23" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService23" android:exported="false" android:=":vlapp23" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape24" android:exported="false" android:=":vlapp24" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider24" android:exported="false" android:=":vlapp24" android:="com.abox.apps.vlite.stub.provider24" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service24" android:exported="false" android:=":vlapp24" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService24" android:exported="false" android:=":vlapp24" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape25" android:exported="false" android:=":vlapp25" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider25" android:exported="false" android:=":vlapp25" android:="com.abox.apps.vlite.stub.provider25" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service25" android:exported="false" android:=":vlapp25" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService25" android:exported="false" android:=":vlapp25" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape26" android:exported="false" android:=":vlapp26" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider26" android:exported="false" android:=":vlapp26" android:="com.abox.apps.vlite.stub.provider26" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service26" android:exported="false" android:=":vlapp26" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService26" android:exported="false" android:=":vlapp26" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape27" android:exported="false" android:=":vlapp27" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider27" android:exported="false" android:=":vlapp27" android:="com.abox.apps.vlite.stub.provider27" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service27" android:exported="false" android:=":vlapp27" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService27" android:exported="false" android:=":vlapp27" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape28" android:exported="false" android:=":vlapp28" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider28" android:exported="false" android:=":vlapp28" android:="com.abox.apps.vlite.stub.provider28" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service28" android:exported="false" android:=":vlapp28" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService28" android:exported="false" android:=":vlapp28" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape29" android:exported="false" android:=":vlapp29" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider29" android:exported="false" android:=":vlapp29" android:="com.abox.apps.vlite.stub.provider29" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service29" android:exported="false" android:=":vlapp29" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService29" android:exported="false" android:=":vlapp29" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2a" android:exported="false" android:=":vlapp2a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2a" android:exported="false" android:=":vlapp2a" android:="com.abox.apps.vlite.stub.provider2a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2a" android:exported="false" android:=":vlapp2a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2a" android:exported="false" android:=":vlapp2a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2b" android:exported="false" android:=":vlapp2b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2b" android:exported="false" android:=":vlapp2b" android:="com.abox.apps.vlite.stub.provider2b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2b" android:exported="false" android:=":vlapp2b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2b" android:exported="false" android:=":vlapp2b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2c" android:exported="false" android:=":vlapp2c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2c" android:exported="false" android:=":vlapp2c" android:="com.abox.apps.vlite.stub.provider2c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2c" android:exported="false" android:=":vlapp2c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2c" android:exported="false" android:=":vlapp2c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2d" android:exported="false" android:=":vlapp2d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2d" android:exported="false" android:=":vlapp2d" android:="com.abox.apps.vlite.stub.provider2d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2d" android:exported="false" android:=":vlapp2d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2d" android:exported="false" android:=":vlapp2d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2e" android:exported="false" android:=":vlapp2e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2e" android:exported="false" android:=":vlapp2e" android:="com.abox.apps.vlite.stub.provider2e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2e" android:exported="false" android:=":vlapp2e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2e" android:exported="false" android:=":vlapp2e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape2f" android:exported="false" android:=":vlapp2f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider2f" android:exported="false" android:=":vlapp2f" android:="com.abox.apps.vlite.stub.provider2f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service2f" android:exported="false" android:=":vlapp2f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService2f" android:exported="false" android:=":vlapp2f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape30" android:exported="false" android:=":vlapp30" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider30" android:exported="false" android:=":vlapp30" android:="com.abox.apps.vlite.stub.provider30" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service30" android:exported="false" android:=":vlapp30" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService30" android:exported="false" android:=":vlapp30" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape31" android:exported="false" android:=":vlapp31" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider31" android:exported="false" android:=":vlapp31" android:="com.abox.apps.vlite.stub.provider31" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service31" android:exported="false" android:=":vlapp31" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService31" android:exported="false" android:=":vlapp31" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape32" android:exported="false" android:=":vlapp32" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider32" android:exported="false" android:=":vlapp32" android:="com.abox.apps.vlite.stub.provider32" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service32" android:exported="false" android:=":vlapp32" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService32" android:exported="false" android:=":vlapp32" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape33" android:exported="false" android:=":vlapp33" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider33" android:exported="false" android:=":vlapp33" android:="com.abox.apps.vlite.stub.provider33" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service33" android:exported="false" android:=":vlapp33" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService33" android:exported="false" android:=":vlapp33" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape34" android:exported="false" android:=":vlapp34" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider34" android:exported="false" android:=":vlapp34" android:="com.abox.apps.vlite.stub.provider34" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service34" android:exported="false" android:=":vlapp34" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService34" android:exported="false" android:=":vlapp34" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape35" android:exported="false" android:=":vlapp35" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider35" android:exported="false" android:=":vlapp35" android:="com.abox.apps.vlite.stub.provider35" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service35" android:exported="false" android:=":vlapp35" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService35" android:exported="false" android:=":vlapp35" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape36" android:exported="false" android:=":vlapp36" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider36" android:exported="false" android:=":vlapp36" android:="com.abox.apps.vlite.stub.provider36" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service36" android:exported="false" android:=":vlapp36" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService36" android:exported="false" android:=":vlapp36" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape37" android:exported="false" android:=":vlapp37" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider37" android:exported="false" android:=":vlapp37" android:="com.abox.apps.vlite.stub.provider37" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service37" android:exported="false" android:=":vlapp37" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService37" android:exported="false" android:=":vlapp37" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape38" android:exported="false" android:=":vlapp38" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider38" android:exported="false" android:=":vlapp38" android:="com.abox.apps.vlite.stub.provider38" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service38" android:exported="false" android:=":vlapp38" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService38" android:exported="false" android:=":vlapp38" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape39" android:exported="false" android:=":vlapp39" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider39" android:exported="false" android:=":vlapp39" android:="com.abox.apps.vlite.stub.provider39" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service39" android:exported="false" android:=":vlapp39" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService39" android:exported="false" android:=":vlapp39" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3a" android:exported="false" android:=":vlapp3a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3a" android:exported="false" android:=":vlapp3a" android:="com.abox.apps.vlite.stub.provider3a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3a" android:exported="false" android:=":vlapp3a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3a" android:exported="false" android:=":vlapp3a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3b" android:exported="false" android:=":vlapp3b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3b" android:exported="false" android:=":vlapp3b" android:="com.abox.apps.vlite.stub.provider3b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3b" android:exported="false" android:=":vlapp3b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3b" android:exported="false" android:=":vlapp3b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3c" android:exported="false" android:=":vlapp3c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3c" android:exported="false" android:=":vlapp3c" android:="com.abox.apps.vlite.stub.provider3c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3c" android:exported="false" android:=":vlapp3c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3c" android:exported="false" android:=":vlapp3c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3d" android:exported="false" android:=":vlapp3d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3d" android:exported="false" android:=":vlapp3d" android:="com.abox.apps.vlite.stub.provider3d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3d" android:exported="false" android:=":vlapp3d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3d" android:exported="false" android:=":vlapp3d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3e" android:exported="false" android:=":vlapp3e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3e" android:exported="false" android:=":vlapp3e" android:="com.abox.apps.vlite.stub.provider3e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3e" android:exported="false" android:=":vlapp3e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3e" android:exported="false" android:=":vlapp3e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape3f" android:exported="false" android:=":vlapp3f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider3f" android:exported="false" android:=":vlapp3f" android:="com.abox.apps.vlite.stub.provider3f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service3f" android:exported="false" android:=":vlapp3f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService3f" android:exported="false" android:=":vlapp3f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape40" android:exported="false" android:=":vlapp40" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider40" android:exported="false" android:=":vlapp40" android:="com.abox.apps.vlite.stub.provider40" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service40" android:exported="false" android:=":vlapp40" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService40" android:exported="false" android:=":vlapp40" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape41" android:exported="false" android:=":vlapp41" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider41" android:exported="false" android:=":vlapp41" android:="com.abox.apps.vlite.stub.provider41" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service41" android:exported="false" android:=":vlapp41" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService41" android:exported="false" android:=":vlapp41" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape42" android:exported="false" android:=":vlapp42" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider42" android:exported="false" android:=":vlapp42" android:="com.abox.apps.vlite.stub.provider42" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service42" android:exported="false" android:=":vlapp42" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService42" android:exported="false" android:=":vlapp42" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape43" android:exported="false" android:=":vlapp43" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider43" android:exported="false" android:=":vlapp43" android:="com.abox.apps.vlite.stub.provider43" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service43" android:exported="false" android:=":vlapp43" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService43" android:exported="false" android:=":vlapp43" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape44" android:exported="false" android:=":vlapp44" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider44" android:exported="false" android:=":vlapp44" android:="com.abox.apps.vlite.stub.provider44" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service44" android:exported="false" android:=":vlapp44" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService44" android:exported="false" android:=":vlapp44" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape45" android:exported="false" android:=":vlapp45" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider45" android:exported="false" android:=":vlapp45" android:="com.abox.apps.vlite.stub.provider45" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service45" android:exported="false" android:=":vlapp45" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService45" android:exported="false" android:=":vlapp45" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape46" android:exported="false" android:=":vlapp46" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider46" android:exported="false" android:=":vlapp46" android:="com.abox.apps.vlite.stub.provider46" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service46" android:exported="false" android:=":vlapp46" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService46" android:exported="false" android:=":vlapp46" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape47" android:exported="false" android:=":vlapp47" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider47" android:exported="false" android:=":vlapp47" android:="com.abox.apps.vlite.stub.provider47" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service47" android:exported="false" android:=":vlapp47" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService47" android:exported="false" android:=":vlapp47" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape48" android:exported="false" android:=":vlapp48" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider48" android:exported="false" android:=":vlapp48" android:="com.abox.apps.vlite.stub.provider48" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service48" android:exported="false" android:=":vlapp48" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService48" android:exported="false" android:=":vlapp48" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape49" android:exported="false" android:=":vlapp49" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider49" android:exported="false" android:=":vlapp49" android:="com.abox.apps.vlite.stub.provider49" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service49" android:exported="false" android:=":vlapp49" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService49" android:exported="false" android:=":vlapp49" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4a" android:exported="false" android:=":vlapp4a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4a" android:exported="false" android:=":vlapp4a" android:="com.abox.apps.vlite.stub.provider4a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4a" android:exported="false" android:=":vlapp4a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4a" android:exported="false" android:=":vlapp4a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4b" android:exported="false" android:=":vlapp4b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4b" android:exported="false" android:=":vlapp4b" android:="com.abox.apps.vlite.stub.provider4b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4b" android:exported="false" android:=":vlapp4b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4b" android:exported="false" android:=":vlapp4b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4c" android:exported="false" android:=":vlapp4c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4c" android:exported="false" android:=":vlapp4c" android:="com.abox.apps.vlite.stub.provider4c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4c" android:exported="false" android:=":vlapp4c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4c" android:exported="false" android:=":vlapp4c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4d" android:exported="false" android:=":vlapp4d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4d" android:exported="false" android:=":vlapp4d" android:="com.abox.apps.vlite.stub.provider4d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4d" android:exported="false" android:=":vlapp4d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4d" android:exported="false" android:=":vlapp4d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4e" android:exported="false" android:=":vlapp4e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4e" android:exported="false" android:=":vlapp4e" android:="com.abox.apps.vlite.stub.provider4e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4e" android:exported="false" android:=":vlapp4e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4e" android:exported="false" android:=":vlapp4e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape4f" android:exported="false" android:=":vlapp4f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider4f" android:exported="false" android:=":vlapp4f" android:="com.abox.apps.vlite.stub.provider4f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service4f" android:exported="false" android:=":vlapp4f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService4f" android:exported="false" android:=":vlapp4f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape50" android:exported="false" android:=":vlapp50" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider50" android:exported="false" android:=":vlapp50" android:="com.abox.apps.vlite.stub.provider50" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service50" android:exported="false" android:=":vlapp50" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService50" android:exported="false" android:=":vlapp50" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape51" android:exported="false" android:=":vlapp51" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider51" android:exported="false" android:=":vlapp51" android:="com.abox.apps.vlite.stub.provider51" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service51" android:exported="false" android:=":vlapp51" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService51" android:exported="false" android:=":vlapp51" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape52" android:exported="false" android:=":vlapp52" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider52" android:exported="false" android:=":vlapp52" android:="com.abox.apps.vlite.stub.provider52" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service52" android:exported="false" android:=":vlapp52" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService52" android:exported="false" android:=":vlapp52" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape53" android:exported="false" android:=":vlapp53" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider53" android:exported="false" android:=":vlapp53" android:="com.abox.apps.vlite.stub.provider53" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service53" android:exported="false" android:=":vlapp53" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService53" android:exported="false" android:=":vlapp53" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape54" android:exported="false" android:=":vlapp54" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider54" android:exported="false" android:=":vlapp54" android:="com.abox.apps.vlite.stub.provider54" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service54" android:exported="false" android:=":vlapp54" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService54" android:exported="false" android:=":vlapp54" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape55" android:exported="false" android:=":vlapp55" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider55" android:exported="false" android:=":vlapp55" android:="com.abox.apps.vlite.stub.provider55" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service55" android:exported="false" android:=":vlapp55" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService55" android:exported="false" android:=":vlapp55" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape56" android:exported="false" android:=":vlapp56" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider56" android:exported="false" android:=":vlapp56" android:="com.abox.apps.vlite.stub.provider56" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service56" android:exported="false" android:=":vlapp56" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService56" android:exported="false" android:=":vlapp56" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape57" android:exported="false" android:=":vlapp57" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider57" android:exported="false" android:=":vlapp57" android:="com.abox.apps.vlite.stub.provider57" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service57" android:exported="false" android:=":vlapp57" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService57" android:exported="false" android:=":vlapp57" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape58" android:exported="false" android:=":vlapp58" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider58" android:exported="false" android:=":vlapp58" android:="com.abox.apps.vlite.stub.provider58" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service58" android:exported="false" android:=":vlapp58" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService58" android:exported="false" android:=":vlapp58" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape59" android:exported="false" android:=":vlapp59" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider59" android:exported="false" android:=":vlapp59" android:="com.abox.apps.vlite.stub.provider59" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service59" android:exported="false" android:=":vlapp59" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService59" android:exported="false" android:=":vlapp59" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5a" android:exported="false" android:=":vlapp5a" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5a" android:exported="false" android:=":vlapp5a" android:="com.abox.apps.vlite.stub.provider5a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5a" android:exported="false" android:=":vlapp5a" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5a" android:exported="false" android:=":vlapp5a" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5b" android:exported="false" android:=":vlapp5b" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5b" android:exported="false" android:=":vlapp5b" android:="com.abox.apps.vlite.stub.provider5b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5b" android:exported="false" android:=":vlapp5b" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5b" android:exported="false" android:=":vlapp5b" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5c" android:exported="false" android:=":vlapp5c" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5c" android:exported="false" android:=":vlapp5c" android:="com.abox.apps.vlite.stub.provider5c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5c" android:exported="false" android:=":vlapp5c" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5c" android:exported="false" android:=":vlapp5c" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5d" android:exported="false" android:=":vlapp5d" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5d" android:exported="false" android:=":vlapp5d" android:="com.abox.apps.vlite.stub.provider5d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5d" android:exported="false" android:=":vlapp5d" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5d" android:exported="false" android:=":vlapp5d" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5e" android:exported="false" android:=":vlapp5e" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5e" android:exported="false" android:=":vlapp5e" android:="com.abox.apps.vlite.stub.provider5e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5e" android:exported="false" android:=":vlapp5e" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5e" android:exported="false" android:=":vlapp5e" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape5f" android:exported="false" android:=":vlapp5f" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider5f" android:exported="false" android:=":vlapp5f" android:="com.abox.apps.vlite.stub.provider5f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service5f" android:exported="false" android:=":vlapp5f" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService5f" android:exported="false" android:=":vlapp5f" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape60" android:exported="false" android:=":vlapp60" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider60" android:exported="false" android:=":vlapp60" android:="com.abox.apps.vlite.stub.provider60" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service60" android:exported="false" android:=":vlapp60" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService60" android:exported="false" android:=":vlapp60" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape61" android:exported="false" android:=":vlapp61" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider61" android:exported="false" android:=":vlapp61" android:="com.abox.apps.vlite.stub.provider61" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service61" android:exported="false" android:=":vlapp61" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService61" android:exported="false" android:=":vlapp61" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape62" android:exported="false" android:=":vlapp62" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider62" android:exported="false" android:=":vlapp62" android:="com.abox.apps.vlite.stub.provider62" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service62" android:exported="false" android:=":vlapp62" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService62" android:exported="false" android:=":vlapp62" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape63" android:exported="false" android:=":vlapp63" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider63" android:exported="false" android:=":vlapp63" android:="com.abox.apps.vlite.stub.provider63" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service63" android:exported="false" android:=":vlapp63" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService63" android:exported="false" android:=":vlapp63" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$Activity64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPortrait64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityLandscape64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIP64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPPortrait64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="portrait" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@style/DefTheme" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityPIPLandscape64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:="true" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialog64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <activity android:="@android:style/Theme.Dialog" android:name="com.vlite.sdk.proxy.ComponentProxyStubs$ActivityDialogLandscape64" android:exported="false" android:=":vlapp64" android:="com.vlite.stub.ta" android:="landscape" android:="colorMode|density|fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" />
        <provider android:="com.vlite.sdk.proxy.ComponentProxyStubs$ContentProvider64" android:exported="false" android:=":vlapp64" android:="com.abox.apps.vlite.stub.provider64" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$Service64" android:exported="false" android:=":vlapp64" />
        <service android:="com.vlite.sdk.proxy.ComponentProxyStubs$ForegroundService64" android:exported="false" android:=":vlapp64" android:="camera|connectedDevice|dataSync|location|mediaPlayback|mediaProjection|microphone|phoneCall" />
        <provider android:="com.vlite.sdk.server.ServerProvider" android:exported="true" android:="@string/vlite_server_process_name" android:="false" android:="com.abox.apps.vlite.provider" />
        <provider android:="com.vlite.sdk.server.proxy.ContentProviderProxy" android:exported="true" android:="@string/vlite_server_process_name" android:="com.abox.apps.cp_proxy" android:="true" />
        <provider android:="com.vlite.sdk.server.proxy.ContentProviderResourceProxy" android:exported="true" android:="@string/vlite_server_process_name" android:="com.abox.apps.cp_resource" android:="true" />
        <activity android:="@android:style/Theme.Translucent.NoTitleBar" android:name="com.vlite.sdk.proxy.PendingActivityProxyStub" android:exported="true" android:="@string/vlite_server_process_name" android:="com.vlite.stub.pending" android:="true" />
        <service android:="com.vlite.sdk.proxy.PendingServiceProxyStub" android:exported="true" android:="@string/vlite_server_process_name" />
        <receiver android:="com.vlite.sdk.proxy.PendingReceiverProxyStub" android:exported="true" android:="@string/vlite_server_process_name" />
        <service android:="com.vlite.sdk.server.virtualservice.job.ProxyJobService" android:="android.permission.BIND_JOB_SERVICE" android:exported="true" android:="@string/vlite_server_process_name" />
        <service android:="com.vlite.sdk.server.virtualservice.job.JobServiceProxyStub" android:="android.permission.BIND_JOB_SERVICE" android:exported="true" android:="@string/vlite_server_process_name" />
        <service android:="com.vlite.sdk.proxy.JobWorkServiceProxyStub" android:="@string/vlite_server_process_name" />
        <activity android:="@style/GoogleAddAccountTheme" android:name="com.vlite.sdk.proxy.GPAddAccountActivity" android:exported="false" android:="@string/vlite_server_process_name" android:="true" android:="keyboard|keyboardHidden|orientation">
            <intent-filter>
                <action android:name="vlite.intent.action.GOOGLE_ADD_ACCOUNT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:="@style/DefAlertTheme" android:name="com.vlite.sdk.proxy.IntentChooserActivity" android:exported="true" android:="@string/vlite_server_process_name" android:="com.vlite.core.choose" android:="true" android:="portrait" android:="keyboard|keyboardHidden|orientation" android:="true">
            <intent-filter>
                <action android:name="vlite.intent.action.CHOOSER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:="@style/LiteTransparentTheme" android:name="com.vlite.sdk.proxy.RequestPermissionsActivity" android:exported="true" android:="@string/vlite_server_process_name" android:="com.vlite.core.request_permission" android:="true" android:="singleInstance" />
        <service android:="com.vlite.sdk.proxy.NotificationServiceStub" android:="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" android:exported="true" android:="@string/vlite_server_process_name">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>
        <service android:="com.vlite.sdk.server.ServerGuardService" android:="@string/vlite_server_process_name" />
        <activity android:="@style/DefAlertTheme" android:name="com.vlite.sdk.proxy.ChooseTypeAndAccountActivity" android:exported="true" android:="@string/vlite_server_process_name" android:="true" android:="true">
            <intent-filter>
                <action android:name="vlite.intent.action.CHOOSE_TYPE_AND_ACCOUNT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:="@style/DefAlertTheme" android:name="com.vlite.sdk.proxy.ChooseAccountTypeActivity" android:exported="true" android:="@string/vlite_server_process_name" android:="true" android:="true">
            <intent-filter>
                <action android:name="vlite.intent.action.CHOOSE_ACCOUNT_TYPE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <service android:="com.vlite.sdk.extension.vpn.ProxyVpnService" android:="android.permission.BIND_VPN_SERVICE" android:exported="true">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>
        <provider android:="com.cardinalcommerce.a.setShadowLayer" android:="true" android:exported="true" android:="com.abox.apps.CCInitProvider" />
        <activity android:="@style/CardinalSDKTheme.ActionBar" android:name="com.cardinalcommerce.a.setTextLocale" android:exported="false" />
        <activity android:="@style/CardinalSDKTheme.ActionBar" android:name="com.cardinalcommerce.a.setTextLocales" android:exported="false" android:="stateAlwaysHidden|adjustResize" />
    </application>
</manifest>