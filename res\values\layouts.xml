<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="layout" name="_xpopup_adapter_text">akd</item>
    <item type="layout" name="_xpopup_adapter_text_match">fa</item>
    <item type="layout" name="_xpopup_attach_impl_list">ady</item>
    <item type="layout" name="_xpopup_attach_popup_view">id</item>
    <item type="layout" name="_xpopup_bottom_impl_list">sb</item>
    <item type="layout" name="_xpopup_bottom_popup_view">aph</item>
    <item type="layout" name="_xpopup_bubble_attach_popup_view">io</item>
    <item type="layout" name="_xpopup_center_impl_confirm">aqi</item>
    <item type="layout" name="_xpopup_center_impl_list">aih</item>
    <item type="layout" name="_xpopup_center_impl_loading">md</item>
    <item type="layout" name="_xpopup_center_popup_view">ain</item>
    <item type="layout" name="_xpopup_divider">nt</item>
    <item type="layout" name="_xpopup_drawer_popup_view">qd</item>
    <item type="layout" name="_xpopup_fullscreen_popup_view">on</item>
    <item type="layout" name="_xpopup_image_viewer_popup_view">ak</item>
    <item type="layout" name="_xpopup_partshadow_popup_view">abd</item>
    <item type="layout" name="_xpopup_position_popup_view">agz</item>
    <item type="layout" name="abc_action_bar_title_item">adi</item>
    <item type="layout" name="abc_action_bar_up_container">ank</item>
    <item type="layout" name="abc_action_menu_item_layout">il</item>
    <item type="layout" name="abc_action_menu_layout">ui</item>
    <item type="layout" name="abc_action_mode_bar">ahv</item>
    <item type="layout" name="abc_action_mode_close_item_material">atk</item>
    <item type="layout" name="abc_activity_chooser_view">aqo</item>
    <item type="layout" name="abc_activity_chooser_view_list_item">vf</item>
    <item type="layout" name="abc_alert_dialog_button_bar_material">aqm</item>
    <item type="layout" name="abc_alert_dialog_material">akb</item>
    <item type="layout" name="abc_alert_dialog_title_material">no</item>
    <item type="layout" name="abc_cascading_menu_item_layout">ff</item>
    <item type="layout" name="abc_dialog_title_material">atb</item>
    <item type="layout" name="abc_expanded_menu_layout">co</item>
    <item type="layout" name="abc_list_menu_item_checkbox">ee</item>
    <item type="layout" name="abc_list_menu_item_icon">tl</item>
    <item type="layout" name="abc_list_menu_item_layout">n</item>
    <item type="layout" name="abc_list_menu_item_radio">t</item>
    <item type="layout" name="abc_popup_menu_header_item_layout">ara</item>
    <item type="layout" name="abc_popup_menu_item_layout">ys</item>
    <item type="layout" name="abc_screen_content_include">gp</item>
    <item type="layout" name="abc_screen_simple">aif</item>
    <item type="layout" name="abc_screen_simple_overlay_action_mode">qs</item>
    <item type="layout" name="abc_screen_toolbar">lc</item>
    <item type="layout" name="abc_search_dropdown_item_icons_2line">ali</item>
    <item type="layout" name="abc_search_view">aed</item>
    <item type="layout" name="abc_select_dialog_material">ack</item>
    <item type="layout" name="abc_tooltip">wc</item>
    <item type="layout" name="activity_about">aag</item>
    <item type="layout" name="activity_auth_ui_layout">nh</item>
    <item type="layout" name="activity_clone">afd</item>
    <item type="layout" name="activity_feedback">afs</item>
    <item type="layout" name="activity_forgot_pwd">akq</item>
    <item type="layout" name="activity_free_try">agj</item>
    <item type="layout" name="activity_google_add_account">afc</item>
    <item type="layout" name="activity_guide_acquire_permission">ez</item>
    <item type="layout" name="activity_html_ui_view">th</item>
    <item type="layout" name="activity_launcher_app">xs</item>
    <item type="layout" name="activity_lock_manager">c</item>
    <item type="layout" name="activity_login">dj</item>
    <item type="layout" name="activity_login_guide">akh</item>
    <item type="layout" name="activity_main">tn</item>
    <item type="layout" name="activity_multi_select_challenge_view">rk</item>
    <item type="layout" name="activity_oob_challenge_view">agp</item>
    <item type="layout" name="activity_order_history">jg</item>
    <item type="layout" name="activity_otp_challenge_view">zr</item>
    <item type="layout" name="activity_recommend">alu</item>
    <item type="layout" name="activity_recommend_app_dialog">hv</item>
    <item type="layout" name="activity_recommend_dialog">fv</item>
    <item type="layout" name="activity_recommend_dialog_land">mc</item>
    <item type="layout" name="activity_saved_records">nb</item>
    <item type="layout" name="activity_settings">aav</item>
    <item type="layout" name="activity_single_select_challenge_view">dz</item>
    <item type="layout" name="activity_splash">eb</item>
    <item type="layout" name="activity_vip_center">cu</item>
    <item type="layout" name="activity_voice_experience">sf</item>
    <item type="layout" name="address_book_footer_item_view">qw</item>
    <item type="layout" name="address_book_list_item_view">yy</item>
    <item type="layout" name="amp_activity_eventexplorer_info">er</item>
    <item type="layout" name="amp_bubble_view">asu</item>
    <item type="layout" name="app_list_layout">amh</item>
    <item type="layout" name="app_not_authorized">al</item>
    <item type="layout" name="app_widget_content_proxy">vs</item>
    <item type="layout" name="app_widget_proxy">li</item>
    <item type="layout" name="applovin_consent_flow_gdpr_are_you_sure_screen">akk</item>
    <item type="layout" name="applovin_consent_flow_gdpr_phase_learn_more_screen">ji</item>
    <item type="layout" name="applovin_consent_flow_gdpr_phase_main_screen">de</item>
    <item type="layout" name="applovin_consent_flow_gdpr_phase_partners_screen">ade</item>
    <item type="layout" name="applovin_debugger_list_item_detail">wn</item>
    <item type="layout" name="applovin_exo_list_divider">adw</item>
    <item type="layout" name="applovin_exo_player_control_view">ajf</item>
    <item type="layout" name="applovin_exo_player_view">ast</item>
    <item type="layout" name="applovin_exo_styled_player_control_ffwd_button">asm</item>
    <item type="layout" name="applovin_exo_styled_player_control_rewind_button">gy</item>
    <item type="layout" name="applovin_exo_styled_player_control_view">ahg</item>
    <item type="layout" name="applovin_exo_styled_player_view">hu</item>
    <item type="layout" name="applovin_exo_styled_settings_list">zg</item>
    <item type="layout" name="applovin_exo_styled_settings_list_item">cj</item>
    <item type="layout" name="applovin_exo_styled_sub_settings_list_item">dh</item>
    <item type="layout" name="applovin_exo_track_selection_dialog">lq</item>
    <item type="layout" name="applovin_native_ad_media_view">anp</item>
    <item type="layout" name="available_paypal_balance_view">tt</item>
    <item type="layout" name="browser_actions_context_menu_page">aid</item>
    <item type="layout" name="browser_actions_context_menu_row">apb</item>
    <item type="layout" name="brvah_quick_view_load_more">age</item>
    <item type="layout" name="bubble_attach_popup">ry</item>
    <item type="layout" name="challege_footer">vx</item>
    <item type="layout" name="challenge_labelview_cf">pj</item>
    <item type="layout" name="challenge_multiselect_body_view">aon</item>
    <item type="layout" name="challenge_oob_body_view">alh</item>
    <item type="layout" name="challenge_otp_body_view">arh</item>
    <item type="layout" name="challenge_single_selectbody">abu</item>
    <item type="layout" name="challenge_toolbar_cf">ni</item>
    <item type="layout" name="choose_account_row">fr</item>
    <item type="layout" name="choose_account_type">aom</item>
    <item type="layout" name="choose_type_and_account">wz</item>
    <item type="layout" name="com_facebook_activity_layout">ri</item>
    <item type="layout" name="com_facebook_device_auth_dialog_fragment">ask</item>
    <item type="layout" name="com_facebook_login_fragment">bn</item>
    <item type="layout" name="com_facebook_smart_device_dialog_fragment">pa</item>
    <item type="layout" name="com_facebook_tooltip_bubble">aos</item>
    <item type="layout" name="common_button_layout">hc</item>
    <item type="layout" name="common_header_layout">rr</item>
    <item type="layout" name="creative_debugger_displayed_ad_detail_activity">ey</item>
    <item type="layout" name="custom_dialog">abk</item>
    <item type="layout" name="custom_notification">lj</item>
    <item type="layout" name="custom_notification_lite">ahc</item>
    <item type="layout" name="default_add_layout">dl</item>
    <item type="layout" name="default_close_layout">gb</item>
    <item type="layout" name="delete_app_layout">wb</item>
    <item type="layout" name="delete_record_file_layout">kx</item>
    <item type="layout" name="design_bottom_navigation_item">mt</item>
    <item type="layout" name="design_bottom_sheet_dialog">bx</item>
    <item type="layout" name="design_layout_snackbar">p</item>
    <item type="layout" name="design_layout_snackbar_include">it</item>
    <item type="layout" name="design_layout_tab_icon">cp</item>
    <item type="layout" name="design_layout_tab_text">ake</item>
    <item type="layout" name="design_menu_item_action_area">aai</item>
    <item type="layout" name="design_navigation_item">aod</item>
    <item type="layout" name="design_navigation_item_header">gz</item>
    <item type="layout" name="design_navigation_item_separator">ado</item>
    <item type="layout" name="design_navigation_item_subheader">rd</item>
    <item type="layout" name="design_navigation_menu">wu</item>
    <item type="layout" name="design_navigation_menu_item">arf</item>
    <item type="layout" name="design_text_input_end_icon">aoz</item>
    <item type="layout" name="design_text_input_start_icon">ku</item>
    <item type="layout" name="dialog_custom_alert">hl</item>
    <item type="layout" name="dialog_grey_app">ru</item>
    <item type="layout" name="dialog_maker_layout">ym</item>
    <item type="layout" name="dialog_save_voice">od</item>
    <item type="layout" name="dialog_select_voice_changing_app">vm</item>
    <item type="layout" name="drop_down_phone_number">oh</item>
    <item type="layout" name="empty_layout">ct</item>
    <item type="layout" name="expand_button">pp</item>
    <item type="layout" name="fingerprint_dialog_layout">nx</item>
    <item type="layout" name="fragment_home">aox</item>
    <item type="layout" name="fragment_incontext_bottom_sheet">abg</item>
    <item type="layout" name="fragment_otp_error">j</item>
    <item type="layout" name="fragment_otp_login">tj</item>
    <item type="layout" name="fragment_otp_phone">ic</item>
    <item type="layout" name="fragment_pypl_transaction_details">vp</item>
    <item type="layout" name="fragment_recording">ue</item>
    <item type="layout" name="fragment_split_login">et</item>
    <item type="layout" name="fragment_web_view">hr</item>
    <item type="layout" name="goods_list_skeleton">ba</item>
    <item type="layout" name="home_bottom_sheet_layout">mj</item>
    <item type="layout" name="image_frame">aoy</item>
    <item type="layout" name="item_app_info">zo</item>
    <item type="layout" name="item_feedback_add">vn</item>
    <item type="layout" name="item_feedback_file_choose">yc</item>
    <item type="layout" name="item_import_app_grid">bk</item>
    <item type="layout" name="layout_default_item_skeleton">u</item>
    <item type="layout" name="layout_dialog_button">ki</item>
    <item type="layout" name="layout_dialog_hide_float_ball_view">jd</item>
    <item type="layout" name="layout_empty_view">ahe</item>
    <item type="layout" name="layout_error_view">ol</item>
    <item type="layout" name="layout_float_ball_window">afi</item>
    <item type="layout" name="layout_goods_item_vertical">anq</item>
    <item type="layout" name="layout_header">adv</item>
    <item type="layout" name="layout_history_item">ng</item>
    <item type="layout" name="layout_login_tip_dialog">cw</item>
    <item type="layout" name="layout_new_version_update_view">aal</item>
    <item type="layout" name="layout_shimmer">aoh</item>
    <item type="layout" name="loading_progress_spinner_view">xj</item>
    <item type="layout" name="m3_alert_dialog">aaa</item>
    <item type="layout" name="m3_alert_dialog_actions">alv</item>
    <item type="layout" name="m3_alert_dialog_title">art</item>
    <item type="layout" name="material_chip_input_combo">abn</item>
    <item type="layout" name="material_clock_display">by</item>
    <item type="layout" name="material_clock_display_divider">gt</item>
    <item type="layout" name="material_clock_period_toggle">ii</item>
    <item type="layout" name="material_clockface_textview">yn</item>
    <item type="layout" name="material_clockface_view">yj</item>
    <item type="layout" name="material_radial_view_group">kb</item>
    <item type="layout" name="material_textinput_timepicker">ve</item>
    <item type="layout" name="material_time_chip">pd</item>
    <item type="layout" name="material_time_input">gg</item>
    <item type="layout" name="material_timepicker">my</item>
    <item type="layout" name="material_timepicker_dialog">lk</item>
    <item type="layout" name="material_timepicker_textinput_display">pn</item>
    <item type="layout" name="max_hybrid_native_ad_view">mi</item>
    <item type="layout" name="max_native_ad_banner_icon_and_text_layout">ahm</item>
    <item type="layout" name="max_native_ad_banner_view">aes</item>
    <item type="layout" name="max_native_ad_leader_view">jo</item>
    <item type="layout" name="max_native_ad_media_banner_view">acz</item>
    <item type="layout" name="max_native_ad_medium_template_1">sx</item>
    <item type="layout" name="max_native_ad_mrec_view">wh</item>
    <item type="layout" name="max_native_ad_recycler_view_item">kq</item>
    <item type="layout" name="max_native_ad_small_template_1">ano</item>
    <item type="layout" name="max_native_ad_vertical_banner_view">yg</item>
    <item type="layout" name="max_native_ad_vertical_leader_view">ahj</item>
    <item type="layout" name="max_native_ad_vertical_media_banner_view">aj</item>
    <item type="layout" name="mediation_debugger_ad_unit_detail_activity">are</item>
    <item type="layout" name="mediation_debugger_list_item_right_detail">sr</item>
    <item type="layout" name="mediation_debugger_list_section">ams</item>
    <item type="layout" name="mediation_debugger_list_section_centered">apl</item>
    <item type="layout" name="mediation_debugger_list_view">rj</item>
    <item type="layout" name="mediation_debugger_multi_ad_activity">yx</item>
    <item type="layout" name="mediation_debugger_text_view_activity">asx</item>
    <item type="layout" name="mtrl_alert_dialog">ia</item>
    <item type="layout" name="mtrl_alert_dialog_actions">aqu</item>
    <item type="layout" name="mtrl_alert_dialog_title">aqw</item>
    <item type="layout" name="mtrl_alert_select_dialog_item">js</item>
    <item type="layout" name="mtrl_alert_select_dialog_multichoice">aqt</item>
    <item type="layout" name="mtrl_alert_select_dialog_singlechoice">ug</item>
    <item type="layout" name="mtrl_calendar_day">zh</item>
    <item type="layout" name="mtrl_calendar_day_of_week">we</item>
    <item type="layout" name="mtrl_calendar_days_of_week">lb</item>
    <item type="layout" name="mtrl_calendar_horizontal">ei</item>
    <item type="layout" name="mtrl_calendar_month">yi</item>
    <item type="layout" name="mtrl_calendar_month_labeled">ant</item>
    <item type="layout" name="mtrl_calendar_month_navigation">aex</item>
    <item type="layout" name="mtrl_calendar_months">abc</item>
    <item type="layout" name="mtrl_calendar_vertical">acu</item>
    <item type="layout" name="mtrl_calendar_year">mh</item>
    <item type="layout" name="mtrl_layout_snackbar">aci</item>
    <item type="layout" name="mtrl_layout_snackbar_include">abf</item>
    <item type="layout" name="mtrl_navigation_rail_item">pf</item>
    <item type="layout" name="mtrl_picker_actions">ya</item>
    <item type="layout" name="mtrl_picker_dialog">atc</item>
    <item type="layout" name="mtrl_picker_fullscreen">adr</item>
    <item type="layout" name="mtrl_picker_header_dialog">dm</item>
    <item type="layout" name="mtrl_picker_header_fullscreen">ahr</item>
    <item type="layout" name="mtrl_picker_header_selection_text">xe</item>
    <item type="layout" name="mtrl_picker_header_title_text">ahp</item>
    <item type="layout" name="mtrl_picker_header_toggle">cv</item>
    <item type="layout" name="mtrl_picker_text_input_date">abm</item>
    <item type="layout" name="mtrl_picker_text_input_date_range">aeo</item>
    <item type="layout" name="native_auth_parent_fragment">xn</item>
    <item type="layout" name="nav_main">se</item>
    <item type="layout" name="notification_action">jv</item>
    <item type="layout" name="notification_action_tombstone">cy</item>
    <item type="layout" name="notification_media_action">acx</item>
    <item type="layout" name="notification_media_cancel_action">hm</item>
    <item type="layout" name="notification_template_big_media">aiy</item>
    <item type="layout" name="notification_template_big_media_custom">mb</item>
    <item type="layout" name="notification_template_big_media_narrow">afy</item>
    <item type="layout" name="notification_template_big_media_narrow_custom">asp</item>
    <item type="layout" name="notification_template_custom_big">jh</item>
    <item type="layout" name="notification_template_icon_group">ut</item>
    <item type="layout" name="notification_template_lines_media">yo</item>
    <item type="layout" name="notification_template_media">aip</item>
    <item type="layout" name="notification_template_media_custom">dc</item>
    <item type="layout" name="notification_template_part_chronometer">ahb</item>
    <item type="layout" name="notification_template_part_time">uq</item>
    <item type="layout" name="payment_source_add_card">rn</item>
    <item type="layout" name="payment_source_native_add_card">aeh</item>
    <item type="layout" name="payment_source_offer_bnpl">vg</item>
    <item type="layout" name="payments_source_card_view">ahn</item>
    <item type="layout" name="paypal_action_button">do</item>
    <item type="layout" name="paypal_action_button_view">aio</item>
    <item type="layout" name="paypal_add_card_body">ie</item>
    <item type="layout" name="paypal_add_card_header">akc</item>
    <item type="layout" name="paypal_add_card_no_fi_header">mk</item>
    <item type="layout" name="paypal_add_card_view">gs</item>
    <item type="layout" name="paypal_address_book_header_view_layout">ll</item>
    <item type="layout" name="paypal_address_book_info_layout">ux</item>
    <item type="layout" name="paypal_alert_toast_view">gd</item>
    <item type="layout" name="paypal_autocomplete_google_text">kf</item>
    <item type="layout" name="paypal_billing_agreement_text_view">bs</item>
    <item type="layout" name="paypal_billing_agreements_info_header">he</item>
    <item type="layout" name="paypal_billing_agreements_terms_body">kd</item>
    <item type="layout" name="paypal_billing_agreements_terms_footer">amq</item>
    <item type="layout" name="paypal_billing_agreements_terms_header">amb</item>
    <item type="layout" name="paypal_billing_agreements_toggle_view">aii</item>
    <item type="layout" name="paypal_bottom_sheet_layout">aer</item>
    <item type="layout" name="paypal_cart_details_list">eh</item>
    <item type="layout" name="paypal_cart_list_item_view">hi</item>
    <item type="layout" name="paypal_compound_header">arx</item>
    <item type="layout" name="paypal_conversion_rate_header_layout">acq</item>
    <item type="layout" name="paypal_conversion_rate_info_view_layout">aln</item>
    <item type="layout" name="paypal_country_picker_body">tw</item>
    <item type="layout" name="paypal_country_picker_fragment">amm</item>
    <item type="layout" name="paypal_country_picker_header">acn</item>
    <item type="layout" name="paypal_crypto_consent_header_view_layout">oa</item>
    <item type="layout" name="paypal_crypto_consent_info_view_layout">agy</item>
    <item type="layout" name="paypal_crypto_currency_conversion_view">dd</item>
    <item type="layout" name="paypal_error_dialog">ahi</item>
    <item type="layout" name="paypal_home_activity">iw</item>
    <item type="layout" name="paypal_legal_agreement_view_layout">qf</item>
    <item type="layout" name="paypal_loading_button">ael</item>
    <item type="layout" name="paypal_loading_spinner">ark</item>
    <item type="layout" name="paypal_logout_view_layout">cl</item>
    <item type="layout" name="paypal_new_shipping_address_review_layout">gn</item>
    <item type="layout" name="paypal_new_shipping_address_search_layout">and</item>
    <item type="layout" name="paypal_payment_button_view">akg</item>
    <item type="layout" name="paypal_payments_button_container_view">aqn</item>
    <item type="layout" name="paypal_primary_split_balance_view">ari</item>
    <item type="layout" name="paypal_profile_header_view_layout">np</item>
    <item type="layout" name="paypal_profile_info_view_layout">aml</item>
    <item type="layout" name="paypal_rate_protection_header_layout">aar</item>
    <item type="layout" name="paypal_rate_protection_info_view_layout">ql</item>
    <item type="layout" name="paypal_secondary_split_balance_view">mz</item>
    <item type="layout" name="paypal_shipping_methods_header_view_layout">yt</item>
    <item type="layout" name="paypal_shipping_methods_info_layout">aaj</item>
    <item type="layout" name="paypal_shipping_methods_list_item_view">ea</item>
    <item type="layout" name="paypal_shipping_option_view">dp</item>
    <item type="layout" name="paypal_shipping_view">sq</item>
    <item type="layout" name="paypal_snapping_recycler_view">te</item>
    <item type="layout" name="paypal_split_balance_view">amz</item>
    <item type="layout" name="paypal_text_input_layout">wt</item>
    <item type="layout" name="paypal_three_ds_v1_header_layout">aje</item>
    <item type="layout" name="paypal_three_ds_v1_step_up_view_layout">aei</item>
    <item type="layout" name="paypal_transaction_details_header_view">apn</item>
    <item type="layout" name="paypal_transaction_details_view_layout">rc</item>
    <item type="layout" name="paypal_user_agreement_text_view">dr</item>
    <item type="layout" name="policies_and_rights_view">kg</item>
    <item type="layout" name="pp_currency_conversion_view_layout">adm</item>
    <item type="layout" name="preference">ju</item>
    <item type="layout" name="preference_category">oi</item>
    <item type="layout" name="preference_category_material">sn</item>
    <item type="layout" name="preference_dialog_edittext">us</item>
    <item type="layout" name="preference_dropdown">aoa</item>
    <item type="layout" name="preference_dropdown_material">aix</item>
    <item type="layout" name="preference_information">e</item>
    <item type="layout" name="preference_information_material">afx</item>
    <item type="layout" name="preference_list_fragment">rm</item>
    <item type="layout" name="preference_material">adb</item>
    <item type="layout" name="preference_recyclerview">xm</item>
    <item type="layout" name="preference_widget_checkbox">kw</item>
    <item type="layout" name="preference_widget_seekbar">ait</item>
    <item type="layout" name="preference_widget_seekbar_material">tz</item>
    <item type="layout" name="preference_widget_switch">g</item>
    <item type="layout" name="preference_widget_switch_compat">oc</item>
    <item type="layout" name="preferred_fi_view_layout">ads</item>
    <item type="layout" name="progress_view_2">wf</item>
    <item type="layout" name="pypl_add_card_fragment">d</item>
    <item type="layout" name="pypl_address_recommendation_fragment">bw</item>
    <item type="layout" name="pypl_billing_agreements_terms_fragment">fx</item>
    <item type="layout" name="pypl_fragment_layout">ajq</item>
    <item type="layout" name="pypl_home_fragment">apx</item>
    <item type="layout" name="pypl_new_shipping_address_fragment_layout">aig</item>
    <item type="layout" name="pypl_new_shipping_address_review_fragment_layout">bh</item>
    <item type="layout" name="pypl_rate_protection_fragment">hx</item>
    <item type="layout" name="pypl_three_ds_fragment">pw</item>
    <item type="layout" name="pypl_user_profile_fragment">wq</item>
    <item type="layout" name="rate_protection_bottom_sheet_layout">acd</item>
    <item type="layout" name="resolve_list_item">ajo</item>
    <item type="layout" name="search_list_add_manually">em</item>
    <item type="layout" name="search_list_country_view">aby</item>
    <item type="layout" name="search_list_item_view">lz</item>
    <item type="layout" name="select_dialog_item_material">uu</item>
    <item type="layout" name="select_dialog_multichoice_material">su</item>
    <item type="layout" name="select_dialog_singlechoice_material">pv</item>
    <item type="layout" name="selectable_item_view">ale</item>
    <item type="layout" name="support_simple_spinner_dropdown_item">vc</item>
    <item type="layout" name="test_action_chip">fs</item>
    <item type="layout" name="test_chip_zero_corner_radius">aso</item>
    <item type="layout" name="test_design_checkbox">bc</item>
    <item type="layout" name="test_design_radiobutton">aao</item>
    <item type="layout" name="test_navigation_bar_item_layout">aem</item>
    <item type="layout" name="test_reflow_chipgroup">qa</item>
    <item type="layout" name="test_toolbar">ape</item>
    <item type="layout" name="test_toolbar_custom_background">aiu</item>
    <item type="layout" name="test_toolbar_elevation">abx</item>
    <item type="layout" name="test_toolbar_surface">oy</item>
    <item type="layout" name="text_info_body_view">aol</item>
    <item type="layout" name="text_view_with_line_height_from_appearance">aki</item>
    <item type="layout" name="text_view_with_line_height_from_layout">anm</item>
    <item type="layout" name="text_view_with_line_height_from_style">ana</item>
    <item type="layout" name="text_view_with_theme_line_height">pt</item>
    <item type="layout" name="text_view_without_line_height">ajk</item>
    <item type="layout" name="three_ds_bottom_sheet_layout">bl</item>
    <item type="layout" name="toast_layout">aqr</item>
    <item type="layout" name="toggle_switch_layout">zt</item>
    <item type="layout" name="top_banner_container">du</item>
    <item type="layout" name="transaction_details_bottom_sheet_layout">rt</item>
    <item type="layout" name="user_profile_bottom_sheet_layout">acj</item>
    <item type="layout" name="venmo_sample_loading">af</item>
    <item type="layout" name="view_button">apr</item>
    <item type="layout" name="view_feedback">alt</item>
    <item type="layout" name="view_float">dt</item>
    <item type="layout" name="view_hide_float">nk</item>
    <item type="layout" name="view_item_record">ajr</item>
    <item type="layout" name="view_item_voice">adz</item>
    <item type="layout" name="view_item_voice_home">xw</item>
    <item type="layout" name="view_loading_layout">agm</item>
    <item type="layout" name="view_login_and_premium">ala</item>
    <item type="layout" name="view_progress">ot</item>
    <item type="layout" name="view_progress_spinner">aps</item>
    <item type="layout" name="view_search_layout">alp</item>
    <item type="layout" name="voice_content_layout">ge</item>
    <item type="layout" name="activity_unlock">aul</item>
</resources>
