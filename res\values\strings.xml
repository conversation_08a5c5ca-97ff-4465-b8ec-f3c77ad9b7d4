<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="about_abox">About Voice</string>
    <string name="abox_premium_privileges">Voice Premium Privileges</string>
    <string name="abox_tips">Voice will keep your account safe.</string>
    <string name="access_device_app_list">3、Access Device App List</string>
    <string name="account_created_successful">Account Created Successful!</string>
    <string name="account_tip">Please enter your email address for verification and password reset.</string>
    <string name="account_tip_1">Please enter your email, and we will create a new account for you in one go.</string>
    <string name="account_tip_2">Please check your email %s for a verification code and enter it below</string>
    <string name="add_account_button_label">Add account</string>
    <string name="add_card_replan_error_desc">Please try another card.</string>
    <string name="add_card_replan_error_header">We had trouble confirming this card</string>
    <string name="add_floating_window">浮窗</string>
    <string name="address_confirmation_header">We found a match for your address</string>
    <string name="address_confirmation_subheader">"We couldn't verify your entry but recommend this match"</string>
    <string name="address_input_header">Address you entered</string>
    <string name="address_rec_header">Recommended match</string>
    <string name="agree_terms">Auto-renewable subscriptions will be automatically renewed within 24 hours prior to the end of the current subscription period. You may disable the auto-renewal at any time in Google Play account Settings after purchase. For more information, please visit our %s</string>
    <string name="ai_boy_2">AI Boy2</string>
    <string name="ai_female">AI Female2</string>
    <string name="ai_female3">AI Female3</string>
    <string name="ai_female4">AI Female4</string>
    <string name="ai_female5">AI Female5</string>
    <string name="ai_girl">AI Girl2</string>
    <string name="ai_girl_3">AI Girl3</string>
    <string name="ai_male2">AI Male2</string>
    <string name="ai_male3">AI Male3</string>
    <string name="ai_male4">AI Male4</string>
    <string name="al_exo_controls_cc_disabled_description">Enable subtitles</string>
    <string name="al_exo_controls_cc_enabled_description">Disable subtitles</string>
    <string name="al_exo_controls_custom_playback_speed">%1$.2fx</string>
    <string name="al_exo_controls_fastforward_description">Fast forward</string>
    <string name="al_exo_controls_fullscreen_enter_description">Enter fullscreen</string>
    <string name="al_exo_controls_fullscreen_exit_description">Exit fullscreen</string>
    <string name="al_exo_controls_hide">Hide player controls</string>
    <string name="al_exo_controls_next_description">Next</string>
    <string name="al_exo_controls_overflow_hide_description">Hide additional settings</string>
    <string name="al_exo_controls_overflow_show_description">Show additional settings</string>
    <string name="al_exo_controls_pause_description">Pause</string>
    <string name="al_exo_controls_play_description">Play</string>
    <string name="al_exo_controls_playback_speed">Speed</string>
    <string name="al_exo_controls_playback_speed_normal">Normal</string>
    <string name="al_exo_controls_previous_description">Previous</string>
    <string name="al_exo_controls_repeat_all_description">Repeat all</string>
    <string name="al_exo_controls_repeat_off_description">Repeat none</string>
    <string name="al_exo_controls_repeat_one_description">Repeat one</string>
    <string name="al_exo_controls_rewind_description">Rewind</string>
    <string name="al_exo_controls_seek_bar_description">Playback progress</string>
    <string name="al_exo_controls_settings_description">Settings</string>
    <string name="al_exo_controls_show">Show player controls</string>
    <string name="al_exo_controls_shuffle_off_description">Shuffle off</string>
    <string name="al_exo_controls_shuffle_on_description">Shuffle on</string>
    <string name="al_exo_controls_stop_description">Stop</string>
    <string name="al_exo_controls_time_placeholder">00:00:00</string>
    <string name="al_exo_controls_vr_description">VR mode</string>
    <string name="al_exo_download_completed">Download completed</string>
    <string name="al_exo_download_description">Download</string>
    <string name="al_exo_download_downloading">Downloading</string>
    <string name="al_exo_download_failed">Download failed</string>
    <string name="al_exo_download_notification_channel_name">Downloads</string>
    <string name="al_exo_download_paused">Downloads paused</string>
    <string name="al_exo_download_paused_for_network">Downloads waiting for network</string>
    <string name="al_exo_download_paused_for_wifi">Downloads waiting for WiFi</string>
    <string name="al_exo_download_removing">Removing downloads</string>
    <string name="al_exo_item_list">%1$s, %2$s</string>
    <string name="al_exo_track_bitrate">%1$.2f Mbps</string>
    <string name="al_exo_track_mono">Mono</string>
    <string name="al_exo_track_resolution">%1$d × %2$d</string>
    <string name="al_exo_track_role_alternate">Alternate</string>
    <string name="al_exo_track_role_closed_captions">CC</string>
    <string name="al_exo_track_role_commentary">Commentary</string>
    <string name="al_exo_track_role_supplementary">Supplementary</string>
    <string name="al_exo_track_selection_auto">Auto</string>
    <string name="al_exo_track_selection_none">None</string>
    <string name="al_exo_track_selection_title_audio">Audio</string>
    <string name="al_exo_track_selection_title_text">Text</string>
    <string name="al_exo_track_selection_title_video">Video</string>
    <string name="al_exo_track_stereo">Stereo</string>
    <string name="al_exo_track_surround">Surround sound</string>
    <string name="al_exo_track_surround_5_point_1">5.1 surround sound</string>
    <string name="al_exo_track_surround_7_point_1">7.1 surround sound</string>
    <string name="al_exo_track_unknown">Unknown</string>
    <string name="amp_label_copied">Copied To Clipboard</string>
    <string name="amp_label_copy">Copy</string>
    <string name="amp_label_device_id">DEVICE ID</string>
    <string name="amp_label_not_avail">N/A</string>
    <string name="amp_label_user_id">USER ID</string>
    <string name="amp_label_user_info">User Information</string>
    <string name="and">and</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="app_is_not_installed">%s is not installed on your phone</string>
    <string name="app_list_des">"Due to the real-time voice modulation feature in Voice, we need to access your phone's app list to facilitate the import of applications from your device. This will enable seamless usage after importing them into Voice."</string>
    <string name="app_name">Voice</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="applovin_agree_message">Please press \"Continue\" if you choose to start using our app.</string>
    <string name="applovin_alt_privacy_policy_text" />
    <string name="applovin_continue_button_text">Continue</string>
    <string name="applovin_creative_debugger_disabled_text">Enable the Creative Debugger SDK settings in order to view recent ads.</string>
    <string name="applovin_creative_debugger_no_ads_text">No recently displayed ads found.</string>
    <string name="applovin_gdpr_advertising_partners_screen_message">As explained in our Privacy Policy, with your consent, we may share this data with the following partners to allow them to serve personalized ads or content:</string>
    <string name="applovin_gdpr_advertising_partners_screen_title">Personalized Ads and Content</string>
    <string name="applovin_gdpr_analytics_partners_screen_message">As explained in our Privacy Policy, with your consent, we may share this data with the following partners to help measure performance and improve our products and services:</string>
    <string name="applovin_gdpr_analytics_partners_screen_title">Performance Measurements and Product Development</string>
    <string name="applovin_gdpr_are_you_sure_screen_message">"Allowing us and/or our partners to use this data for personalized ads and content and other purposes, as described in our Privacy Policy:

• Gives you a more personalized experience, ads, and content

• Helps support product improvements and developments

• Allows you to participate in promotions"</string>
    <string name="applovin_gdpr_are_you_sure_screen_title">Are you sure?</string>
    <string name="applovin_gdpr_back_button_text">Back</string>
    <string name="applovin_gdpr_learn_more_screen_bullet_1">Helping keep our App free!</string>
    <string name="applovin_gdpr_learn_more_screen_bullet_2">Giving you a more personal experience, including personalized ads and content</string>
    <string name="applovin_gdpr_learn_more_screen_bullet_3">Helping support product improvements and developments</string>
    <string name="applovin_gdpr_learn_more_screen_bullet_4">Allowing you to participate in promotions</string>
    <string name="applovin_gdpr_learn_more_screen_message_1">Thank you for downloading our App! Allowing us and our partners to use this data helps us create a unique App experience, including by:</string>
    <string name="applovin_gdpr_learn_more_screen_message_2">You can always change your selections later. If you choose not to share your data, you will still be able to access the App and you will still receive ads. However, those ads may be less relevant to your interests.</string>
    <string name="applovin_gdpr_main_screen_analytics_purposes_switch_text">Performance measurements and product development</string>
    <string name="applovin_gdpr_main_screen_learn_more_button_text">Learn More</string>
    <string name="applovin_gdpr_main_screen_message">"As explained in our Privacy Policy, with your consent, we collect certain information concerning your device, such as IP address and advertising identifiers. This data allows us and our partners to serve personalized ads and content and make improvements.

Below you can choose to allow us and/or our partners to use this data for the following purposes:"</string>
    <string name="applovin_gdpr_main_screen_personalized_advertising_purposes_switch_text">Personalized ads and content</string>
    <string name="applovin_gdpr_main_screen_pp_and_tos_switch_text">I am over the age of sixteen, accept the Terms of Use, and acknowledge the Privacy Policy applies.</string>
    <string name="applovin_gdpr_main_screen_privacy_policy_switch_text">I certify that I am over the age of sixteen and that I have read and understood the Privacy Policy.</string>
    <string name="applovin_gdpr_main_screen_title">"Thank you for downloading 
%s!"</string>
    <string name="applovin_gdpr_understand_and_continue_button_text">I understand and confirm</string>
    <string name="applovin_learn_more_screen_title">Help us keep %s free and more fun for you!</string>
    <string name="applovin_list_item_image_description">Displays more info about the list item.</string>
    <string name="applovin_pp_and_tos_title">To use %s you must agree to our Terms &amp; Conditions and affirm you have reviewed our Privacy Policy</string>
    <string name="applovin_pp_title">To use %s you must affirm that you have reviewed and acknowledged our Privacy Policy.</string>
    <string name="applovin_privacy_policy_text">Privacy Policy</string>
    <string name="applovin_terms_of_service_text">Terms &amp; Conditions</string>
    <string name="applovin_terms_of_use_text">Terms of Use</string>
    <string name="apply">Apply</string>
    <string name="are_you_sure_you_want_to_delete_this_file">Are you sure you want to delete this file?</string>
    <string name="audio_recording_title">Audio Recording Title</string>
    <string name="auth_gls_name_checking_info_title">Checking info…</string>
    <string name="banner_adapter_null_error">Adapter为空！请检查下参数</string>
    <string name="battery_opt">Battery optimization whitelist</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="brvah_load_complete">点击加载更多</string>
    <string name="brvah_load_end">没有更多数据</string>
    <string name="brvah_load_failed">加载失败，请点我重试</string>
    <string name="brvah_loading">正在加载中...</string>
    <string name="btn_clone">Start Voice Changing (%1$d)</string>
    <string name="btn_negative_button">Cancel</string>
    <string name="btn_positive_button">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_anytime">Cancel anytime</string>
    <string name="card_network">Card Network</string>
    <string name="challInfoText">Verification code</string>
    <string name="change_to_this_voice">Change to this voice</string>
    <string name="change_voice_succeed">Change Voice Successful</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="chip_text">Chip text</string>
    <string name="choose">Choose</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="clone_error">The application import failed. Please try again</string>
    <string name="code">Code</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id">00000000000000000000000000000000</string>
    <string name="com_facebook_device_auth_instructions">Visit &lt;b>facebook.com/device&lt;/b> and enter the code shown above.</string>
    <string name="com_facebook_image_download_unknown_error">Unexpected error while downloading an image.</string>
    <string name="com_facebook_internet_permission_error_message">WebView login requires INTERNET permission</string>
    <string name="com_facebook_internet_permission_error_title">AndroidManifest Error</string>
    <string name="com_facebook_like_button_liked">Liked</string>
    <string name="com_facebook_like_button_not_liked">Like</string>
    <string name="com_facebook_loading">Loading…</string>
    <string name="com_facebook_loginview_cancel_action">Cancel</string>
    <string name="com_facebook_loginview_log_in_button">Log in</string>
    <string name="com_facebook_loginview_log_in_button_continue">Continue with Facebook</string>
    <string name="com_facebook_loginview_log_in_button_long">Log in with Facebook</string>
    <string name="com_facebook_loginview_log_out_action">Log out</string>
    <string name="com_facebook_loginview_log_out_button">Log out</string>
    <string name="com_facebook_loginview_logged_in_as">Logged in as: %1$s</string>
    <string name="com_facebook_loginview_logged_in_using_facebook">Logged in using Facebook</string>
    <string name="com_facebook_send_button_text">Send</string>
    <string name="com_facebook_share_button_text">Share</string>
    <string name="com_facebook_smart_device_instructions">To connect your account, open the Facebook app on your mobile device and check for notifications.</string>
    <string name="com_facebook_smart_device_instructions_or">- OR -</string>
    <string name="com_facebook_smart_login_confirmation_cancel">Not you?</string>
    <string name="com_facebook_smart_login_confirmation_continue_as">Continue as %1$s</string>
    <string name="com_facebook_smart_login_confirmation_title">Confirm Login</string>
    <string name="com_facebook_tooltip_default">"You're in control - choose what info you want to share with apps."</string>
    <string name="common_google_play_services_enable_button">Enable</string>
    <string name="common_google_play_services_enable_text">"%1$s won't work unless you enable Google Play services."</string>
    <string name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text">"%1$s won't run without Google Play services, which are missing from your device."</string>
    <string name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue">%1$s is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text">"%1$s won't run without Google Play services, which are not supported by your device."</string>
    <string name="common_google_play_services_update_button">Update</string>
    <string name="common_google_play_services_update_text">"%1$s won't run unless you update Google Play services."</string>
    <string name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text">"%1$s won't run without Google Play services, which are currently updating."</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="common_signin_button_text">Sign in</string>
    <string name="common_signin_button_text_long">Sign in with Google</string>
    <string name="confirm_device_credential_password">Use password</string>
    <string name="contact_us">Contact Us</string>
    <string name="continueText">CONTINUE</string>
    <string name="continue_name">Continue</string>
    <string name="continue_with_email">Continue with Email</string>
    <string name="continue_with_facebook">Continue with Facebook</string>
    <string name="continue_with_google">Continue with Google</string>
    <string name="copy">Copy</string>
    <string name="copy_toast_msg">Link copied to clipboard</string>
    <string name="copyright_2023_abox">Copyright@2023 Voice</string>
    <string name="create_account">Create account</string>
    <string name="create_shortcut">Create ShortCut</string>
    <string name="default_error_msg">Unknown error</string>
    <string name="default_web_client_id">*************-evnrkdh92ne6seh8l7mvmlqrf8iobhme.apps.googleusercontent.com</string>
    <string name="delete">Delete</string>
    <string name="delete_confirmation">Delete Confirmation</string>
    <string name="delete_floating_window">删除浮窗</string>
    <string name="discord">Discord</string>
    <string name="display_over_other_apps">Display over other apps</string>
    <string name="download_failure">download failed</string>
    <string name="edit_shipping_address">Edit shipping address</string>
    <string name="email">Email：</string>
    <string name="email_notify_tips">We will notify you as soon as we solve it</string>
    <string name="empty_app_tips">There is no app installed on your phone. Please download the app before importing it to Voice for use</string>
    <string name="enable">Enable</string>
    <string name="enabled">Enabled</string>
    <string name="error_browser_not_found">No installed activities can open this URL: %1$s</string>
    <string name="error_device_not_configured_for_deep_link">"The return url scheme was not set up, incorrectly set up, or more than one Activity on this device defines the same url scheme in it's Android Manifest. See https://github.com/braintree/browser-switch-android for more information on setting up a return url scheme."</string>
    <string name="error_icon_content_description">Error</string>
    <string name="error_incorrect_username_or_password">Incorrect username or password, please try again</string>
    <string name="error_invalid_email_address">Please enter a valid email address.</string>
    <string name="error_invalid_pwd">Password format is incorrect.</string>
    <string name="error_message_change_not_allowed">El administrador no permite realizar este cambio</string>
    <string name="error_request_code_invalid">Request code cannot be Integer.MIN_VALUE</string>
    <string name="error_return_url_required">A returnUrlScheme is required.</string>
    <string name="expand_button_title">Advanced</string>
    <string name="expiration_date">Expiration date：%S</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="facebook_app_id">672325475062719</string>
    <string name="facebook_client_token">********************************</string>
    <string name="fallback_menu_item_copy_link">Copy link</string>
    <string name="fallback_menu_item_open_in_browser">Open in browser</string>
    <string name="fallback_menu_item_share_link">Share link</string>
    <string name="fb_login_protocol_scheme">https://abox-37e50.firebaseapp.com/__/auth/handler</string>
    <string name="feedback">Feedback</string>
    <string name="feedback_error">Failed to submit feedback</string>
    <string name="feedbacking">Submitting feedback</string>
    <string name="file_name_empty_tip">File name cannot be empty.</string>
    <string name="file_name_exists_tip">File with the same name already exists.</string>
    <string name="fingerprint_dialog_icon_description">Fingerprint icon</string>
    <string name="fingerprint_dialog_touch_sensor">Touch the fingerprint sensor</string>
    <string name="fingerprint_error_hw_not_available">Fingerprint hardware not available.</string>
    <string name="fingerprint_error_hw_not_present">This device does not have a fingerprint sensor</string>
    <string name="fingerprint_error_lockout">Too many attempts. Please try again later.</string>
    <string name="fingerprint_error_no_fingerprints">No fingerprints enrolled.</string>
    <string name="fingerprint_error_user_canceled">Fingerprint operation canceled by user.</string>
    <string name="fingerprint_not_recognized">Not recognized</string>
    <string name="float_ball_permission">2、Floating Ball Permission</string>
    <string name="float_ball_permission_desc">Instantly switch sound effects during use for convenience.</string>
    <string name="float_ball_permission_subtitle">"Grant Voice the 'Display over other apps' permission for a seamless and convenient real-time voice modulation experience."</string>
    <string name="for_successful_usage_we_may_need_the_following_permissions">For successful usage, we may need the following permissions:</string>
    <string name="forgot_password">Forgot password</string>
    <string name="forgot_password_flag">Forgot password?</string>
    <string name="free_fire_launcher_tips">To run %s, you need to log in to Google Play to successfully download resource files.</string>
    <string name="free_fire_login_title_tips">Why does launching Free Fire require logging in to Google Play?</string>
    <string name="free_try_use_protocol">"Receive a 24-hour notice before your trial ends. Billing starts after the trial period. Confirm you're 18+, agree to our %s and understand no refunds for partial periods."</string>
    <string name="gcm_defaultSenderId">*************</string>
    <string name="generic_error_no_device_credential">No PIN, pattern, or password set.</string>
    <string name="generic_error_no_keyguard">This device does not support PIN, pattern, or password.</string>
    <string name="generic_error_user_canceled">Authentication canceled by user.</string>
    <string name="get_it_on">get it on</string>
    <string name="get_started">Get Started</string>
    <string name="go_to_settings">Go To Settings</string>
    <string name="google_api_key">AIzaSyAFRtilVNen6o5_S4EGZWPVrNpl_KMpTG4</string>
    <string name="google_app_id">1:*************:android:7f8f659acafae08ad7e7ac</string>
    <string name="google_crash_reporting_api_key">AIzaSyAFRtilVNen6o5_S4EGZWPVrNpl_KMpTG4</string>
    <string name="google_play">Google Play</string>
    <string name="google_storage_bucket">abox-37e50.appspot.com</string>
    <string name="guide_des">Different devices may have varying methods of operation. If you encounter any issues, please feel free to contact us for assistance.</string>
    <string name="herman">Herman</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="hide_float_ball_content">Are you sure you want to hide the Voice floating ball?</string>
    <string name="hide_float_ball_tip">(Floating ball will appear again upon next app launch)</string>
    <string name="hide_float_positive_content">Hide Voice floating ball</string>
    <string name="hold_to_talk">Hold to talk</string>
    <string name="home">Home</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="id">ID : %s</string>
    <string name="import_success">Import Successful</string>
    <string name="improve_abox_stability">Improve Voice Stability</string>
    <string name="indicator_color_error">指示器的颜色只能设置成“@color/colorID or #000000”</string>
    <string name="install_tips">Sorry, due to security and compliance considerations, this application cannot be used in Voice.</string>
    <string name="issuer_image">Issuer Image</string>
    <string name="item_view_role_description">Tab</string>
    <string name="join_us">Join Us:</string>
    <string name="label_loading_list">loading</string>
    <string name="label_search_app">Search App Names</string>
    <string name="launcher_tip">In order to achieve the best voice changing effect, please log in to %s again in the Voice dedicated environment. Your account information is safe. Thank you for your understanding!</string>
    <string name="learn_more_about_auth">Learn more about auth</string>
    <string name="lifetime_honorary_member">Lifetime Honorary Member</string>
    <string name="lifetime_member">Lifetime Member</string>
    <string name="lifetime_tips">You are already a lifetime member, no need to purchase again.</string>
    <string name="lock_in_task_manager">Lock Voice in the task manager</string>
    <string name="lock_title">Lock Voice in the task manager</string>
    <string name="log_in">Log In</string>
    <string name="login_failed_please_try_again">Login failed, please try again.</string>
    <string name="logout">LOGOUT</string>
    <string name="logout_message">Confirm logout?</string>
    <string name="m3_ref_typeface_brand_display_regular">sans-serif</string>
    <string name="m3_ref_typeface_brand_medium">sans-serif-medium</string>
    <string name="m3_ref_typeface_brand_regular">sans-serif</string>
    <string name="m3_ref_typeface_plain_medium">sans-serif-medium</string>
    <string name="m3_ref_typeface_plain_regular">sans-serif</string>
    <string name="m3_sys_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="m3_sys_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="m3_sys_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_standard">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="m3_sys_typescale_body_large_font">sans-serif</string>
    <string name="m3_sys_typescale_body_medium_font">sans-serif</string>
    <string name="m3_sys_typescale_body_small_font">sans-serif</string>
    <string name="m3_sys_typescale_display_large_font">sans-serif</string>
    <string name="m3_sys_typescale_display_medium_font">sans-serif</string>
    <string name="m3_sys_typescale_display_small_font">sans-serif</string>
    <string name="m3_sys_typescale_headline_large_font">sans-serif</string>
    <string name="m3_sys_typescale_headline_medium_font">sans-serif</string>
    <string name="m3_sys_typescale_headline_small_font">sans-serif</string>
    <string name="m3_sys_typescale_label_large_font">sans-serif-medium</string>
    <string name="m3_sys_typescale_label_medium_font">sans-serif-medium</string>
    <string name="m3_sys_typescale_label_small_font">sans-serif-medium</string>
    <string name="m3_sys_typescale_title_large_font">sans-serif</string>
    <string name="m3_sys_typescale_title_medium_font">sans-serif-medium</string>
    <string name="m3_sys_typescale_title_small_font">sans-serif-medium</string>
    <string name="material_clock_display_divider">:</string>
    <string name="material_clock_toggle_content_description">Select AM or PM</string>
    <string name="material_hour_selection">Select hour</string>
    <string name="material_hour_suffix">"%1$s o'clock"</string>
    <string name="material_minute_selection">Select minutes</string>
    <string name="material_minute_suffix">%1$s minutes</string>
    <string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string name="material_timepicker_hour">Hour</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string name="max_select_pics">Add up to three pictures</string>
    <string name="max_upload_tops">The total upload size cannot exceed 200MB, please delete some files and try again!</string>
    <string name="message_app_clones">app clones</string>
    <string name="message_create_shortcut_loading">Creating Shortcut</string>
    <string name="message_install_undone">install undone</string>
    <string name="message_loading_start">Starting</string>
    <string name="message_required_permission_denied">"To ensure the normal use of the application on Voice, please open the necessary permissions
 require Files and media permission"</string>
    <string name="message_shortcut_create">you have tried to add it to the launcher. If the addition fails, please go to the system settings and open the \"create desktop shortcut\" permission.</string>
    <string name="message_stop_save_log">Saved to %s</string>
    <string name="message_too_short">Message too short</string>
    <string name="microphone_permission">1、Microphone Permission</string>
    <string name="microphone_permission_desc">To let you fully enjoy the fun of voice Change, please allow us to use your microphone. We will ensure the security of your privacy.</string>
    <string name="monster">Monster</string>
    <string name="more">More</string>
    <string name="more_payment_methods_coming_soon">More payment methods coming soon</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">@android:string/cancel</string>
    <string name="mtrl_picker_confirm">OK</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="mtrl_timepicker_confirm">OK</string>
    <string name="my_apps">My Apps</string>
    <string name="nav_app_bar_navigate_up_description">Navigate up</string>
    <string name="nav_app_bar_open_drawer_description">Open navigation drawer</string>
    <string name="need_some_help">Need some help?</string>
    <string name="new_code_message">We sent you a new code</string>
    <string name="next">NEXT</string>
    <string name="noApplications">No find applications</string>
    <string name="no_ads">NO ADS</string>
    <string name="no_more_reminders_next_time">No more reminders next time.</string>
    <string name="no_recordings_yet">No recordings yet</string>
    <string name="none_selected_id">No currently selected product IDs</string>
    <string name="not_granted_record_permission">Please agree to the recording permission</string>
    <string name="not_logged">Not Logged</string>
    <string name="not_set">Not set</string>
    <string name="not_support_app_tips">"Voice does not currently support the %s app due to compatibility issues. We're actively working on a fix. Thanks for your understanding."</string>
    <string name="not_support_subtitle">Join the official Voice group for the latest updates.</string>
    <string name="ok">OK</string>
    <string name="order_history">Order History</string>
    <string name="order_id">Order ID:%d</string>
    <string name="original_voice">Original Voice</string>
    <string name="other_plan">Other Plan</string>
    <string name="owner_name">Admin</string>
    <string name="partial_privileges_coming_soon">Partial privileges coming soon</string>
    <string name="password">Password：</string>
    <string name="password_reset_successful">Password Reset Successful!</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="pay_success_tips">You have successfully subscribed to the Premium service. Now, enjoy your new voices!</string>
    <string name="payment_security">Payment Security</string>
    <string name="paypal">Paypal</string>
    <string name="paypal_3ds_add_new_fi">Your card issuer wasn’t able to confirm your card. Add a new card to continue.</string>
    <string name="paypal_3ds_alternate_fi">Your card issuer wasn’t able to confirm your card. Choose another way to pay.</string>
    <string name="paypal_3ds_generic">Your card issuer wasn’t able to confirm your card. Choose another way to pay or add a new card.</string>
    <string name="paypal_amplitude_dev_1p_key">********************************************</string>
    <string name="paypal_amplitude_dev_3p_key">********************************************</string>
    <string name="paypal_amplitude_prod_1p_key">********************************************</string>
    <string name="paypal_amplitude_prod_3p_key">********************************************</string>
    <string name="paypal_auth_common_header_back_press">back press</string>
    <string name="paypal_auth_common_header_close">close</string>
    <string name="paypal_auth_common_header_paypal_logo">PayPal logo</string>
    <string name="paypal_auth_otp_login_back_press">back press</string>
    <string name="paypal_auth_otp_login_check_code">Check your code and try again.</string>
    <string name="paypal_auth_otp_login_close">close</string>
    <string name="paypal_auth_otp_login_code_expired">Your code has expired. Get new code.</string>
    <string name="paypal_auth_otp_login_code_sent_to">Code sent to %s</string>
    <string name="paypal_auth_otp_login_error_use_password_instead">Use Password Instead</string>
    <string name="paypal_auth_otp_login_get_a_code">Get a code</string>
    <string name="paypal_auth_otp_login_get_new_code">Get new code</string>
    <string name="paypal_auth_otp_login_log_in">Log In</string>
    <string name="paypal_auth_otp_login_log_in_fast_with_a_one_time_code">Log in fast with a one-time code</string>
    <string name="paypal_auth_otp_login_otp_phone_header">"Enter your 
 one-time code"</string>
    <string name="paypal_auth_otp_login_paypal_logo">PayPal logo</string>
    <string name="paypal_auth_otp_login_paypal_logo_content_description">PayPal logo</string>
    <string name="paypal_auth_otp_login_phone_text">phone</string>
    <string name="paypal_auth_otp_login_something_went_wrong">Something went wrong</string>
    <string name="paypal_auth_otp_login_tcpa_consent">By continuing, you confirm that you are authorized to use this phone number and agree to receive text messages. Carrier fees may apply.</string>
    <string name="paypal_auth_otp_login_try_again_later">You can try again later or log in with your password.</string>
    <string name="paypal_auth_otp_login_use_password_instead">Use password instead</string>
    <string name="paypal_auth_otp_login_we_ll_send_a_code_to">"We'll send a code to:"</string>
    <string name="paypal_auth_split_login_email">Email</string>
    <string name="paypal_auth_split_login_enter_an_email_address">Enter an email address.</string>
    <string name="paypal_auth_split_login_enter_your_email_to_get_started">Enter your email to get started.</string>
    <string name="paypal_auth_split_login_forgot_your_login_info">Forgot your login info?</string>
    <string name="paypal_auth_split_login_next">Next</string>
    <string name="paypal_auth_split_login_pay_with_paypal">Pay with PayPal</string>
    <string name="paypal_checkout_00_00gb_dont_translate">$00.00GB</string>
    <string name="paypal_checkout_1234_dont_translate">....1234</string>
    <string name="paypal_checkout_1_usd_0_7607_gbp_dont_translate">$1 USD = £0.7607 GBP</string>
    <string name="paypal_checkout_1_usd_dont_translate">1 USD = Blah</string>
    <string name="paypal_checkout_3ds_code">Code</string>
    <string name="paypal_checkout_3ds_loading">We’re confirming your info with your card issuer</string>
    <string name="paypal_checkout_3ds_loading_with_issuer_name">We’re confirming your info with %1$s</string>
    <string name="paypal_checkout_3ds_next">Next</string>
    <string name="paypal_checkout_3ds_resend">Resend</string>
    <string name="paypal_checkout_3ds_secure_authentication">PayPal Secure Authentication</string>
    <string name="paypal_checkout_3ds_submit">Submit</string>
    <string name="paypal_checkout_about_payment_content_1">PayPal allows you to make payments using a variety of methods including: PayPal Cash or PayPal Cash Plus account balance, a bank account, PayPal Credit, debit or credit cards, and rewards balance.</string>
    <string name="paypal_checkout_about_payment_content_2">This page does not address unauthorized transactions. For information on your protection against unauthorized transactions, please see the Error Resolution section of the PayPal User Agreement.</string>
    <string name="paypal_checkout_about_payment_content_3">You can choose any of the payment methods in your account with PayPal as your preferred payment method in the Payments section of your account settings. If you select a preferred payment method, it will be shown as the preferred payment method when you make a purchase online, in-store, or when you send money via goods and services.</string>
    <string name="paypal_checkout_about_payment_content_4">You can select different preferred payment methods for online transactions and in-store transactions.</string>
    <string name="paypal_checkout_about_payment_methods">About Payment Methods</string>
    <string name="paypal_checkout_about_payment_sub_header">Selecting a preferred payment method</string>
    <string name="paypal_checkout_add_card">Add Card</string>
    <string name="paypal_checkout_add_card_address_line_1_error">Add a Street address</string>
    <string name="paypal_checkout_add_card_billing_address">Billing details</string>
    <string name="paypal_checkout_add_card_button">Add card button</string>
    <string name="paypal_checkout_add_card_city_error">Add a City</string>
    <string name="paypal_checkout_add_card_country_us">United states</string>
    <string name="paypal_checkout_add_card_error">Things don’t appear to be working at the moment. Please try again later.</string>
    <string name="paypal_checkout_add_card_error_desc">There seems to be a problem confirming your info with %1$s %2$s. Try again later.</string>
    <string name="paypal_checkout_add_card_error_header">We’re unable to link your card right now</string>
    <string name="paypal_checkout_add_card_full_header">Add a new card</string>
    <string name="paypal_checkout_add_card_generic_field_error">This field is required</string>
    <string name="paypal_checkout_add_card_label_error">Enter the correct %1$s</string>
    <string name="paypal_checkout_add_card_message">"To add a bank or card, you'll need to complete your purchase on PayPal.com."</string>
    <string name="paypal_checkout_add_card_no_fi_full_header">Enter your info to pay</string>
    <string name="paypal_checkout_add_card_plus">+ Add Card</string>
    <string name="paypal_checkout_add_card_processor_error">Invalid card type. Please try a different card.</string>
    <string name="paypal_checkout_add_card_state_error">Add a State</string>
    <string name="paypal_checkout_add_card_success">You added a card</string>
    <string name="paypal_checkout_add_card_success_confirmed">Your %1$s …%2$s is confirmed. You can now use this card for purchases.</string>
    <string name="paypal_checkout_add_card_success_linked">You’ve successfully linked %1$s …%2$s</string>
    <string name="paypal_checkout_add_card_validation_generic_error">Some of your information is missing or incorrect. Please check and try again.</string>
    <string name="paypal_checkout_add_card_zipcode_error">Add a ZIP Code</string>
    <string name="paypal_checkout_add_debit_or_credit">Add a new debit or credit card</string>
    <string name="paypal_checkout_add_fi">Add a new debit or credit card</string>
    <string name="paypal_checkout_add_manually">Add manually</string>
    <string name="paypal_checkout_add_new_address">Add</string>
    <string name="paypal_checkout_add_shipping_add_manually">Add manually</string>
    <string name="paypal_checkout_add_shipping_address">Add an address</string>
    <string name="paypal_checkout_add_shipping_address_failure">There was an error adding a new Shipping address</string>
    <string name="paypal_checkout_add_shipping_address_success">You added a new Shipping address</string>
    <string name="paypal_checkout_address_book">Address Book</string>
    <string name="paypal_checkout_address_book_circle_image">Address Book Circle Image</string>
    <string name="paypal_checkout_address_label">Address</string>
    <string name="paypal_checkout_address_translatable">Address</string>
    <string name="paypal_checkout_address_validation_error">"We couldn't validate that address. Please check your entry and try again."</string>
    <string name="paypal_checkout_agree_terms">You agree to terms.</string>
    <string name="paypal_checkout_alphabet">Alphabet</string>
    <string name="paypal_checkout_apply_for_credit">Apply For Credit</string>
    <string name="paypal_checkout_apply_for_pay_in_four">Apply for Pay in 4</string>
    <string name="paypal_checkout_apply_for_paypal_credit">Apply for PayPal Credit</string>
    <string name="paypal_checkout_apply_now">Apply Now</string>
    <string name="paypal_checkout_attention_message">Your card issuer will determine the currency conversion rate and what fees they may charge. Check your statement for the final amount.</string>
    <string name="paypal_checkout_backup">Backup:</string>
    <string name="paypal_checkout_backup_amex_dont_translate">Backup: Amex 5508</string>
    <string name="paypal_checkout_backup_colon_dynamic">Backup: %1$s</string>
    <string name="paypal_checkout_backup_visa_1354_dont_translate">Backup: Visa 1354</string>
    <string name="paypal_checkout_bad_balance">Your %1$s balance</string>
    <string name="paypal_checkout_balance_not_supported">PayPal balance not supported by this funding option</string>
    <string name="paypal_checkout_billing_agreeements_terms_no_purchase">"We'll save your payment and shipping address preferences for future purchases at this merchant. If your chosen payment method is unavailable, PayPal can use other payment methods linked to your account according to PayPal Policies. To change these preferences or cancel the authorization at this merchant, visit PayPal settings."</string>
    <string name="paypal_checkout_billing_agreements_agree_and_continue">Agree &amp; Continue</string>
    <string name="paypal_checkout_billing_agreements_always_use_balance">Always use balance first</string>
    <string name="paypal_checkout_billing_agreements_authorization_info">Authorization info</string>
    <string name="paypal_checkout_billing_agreements_authorize_and_continue">Authorize &amp; Continue</string>
    <string name="paypal_checkout_billing_agreements_details">Details</string>
    <string name="paypal_checkout_billing_agreements_info_header_title">Authorize %1$s to charge PayPal for future purchases</string>
    <string name="paypal_checkout_billing_agreements_info_header_title_eu">Authorize your future PayPal payments directly on %1$s</string>
    <string name="paypal_checkout_billing_agreements_legal_text">"We'll save these selections for future payments to %1$s. If you have a PayPal balance, we'll always use that first. #lAuthorization info#l"</string>
    <string name="paypal_checkout_billing_agreements_legal_text_eu">Authorize this purchase and skip PayPal log in for future purchases at %1$s. #lDetails#l</string>
    <string name="paypal_checkout_billing_agreements_terms_eu_no_purchase">This will enable you to skip PayPal login and make payments faster on %1$s. For more information, see billing agreement payments in #lPayPal User Agreement#l. You can manage this in your PayPal account settings.</string>
    <string name="paypal_checkout_billing_agreements_terms_eu_purchase">"This will enable faster payments at %1$s so you won't need to log in for future purchases. If you have a PayPal balance, it will be applied first in future transactions. For more information, see billing agreement payments in #lPayPal User Agreement#l. You can manage this in your PayPal account settings."</string>
    <string name="paypal_checkout_billing_agreements_terms_footer_link">Payment method rights</string>
    <string name="paypal_checkout_billing_agreements_terms_header">Authorization Info</string>
    <string name="paypal_checkout_billing_agreements_terms_purchase">"We'll save your payment and shipping address preferences for future purchases at this merchant. If you have a PayPal balance, it will always be applied first. If your chosen payment method is unavailable, PayPal can use other payment methods linked to your account according to PayPal Policies. To change these preferences or cancel the authorization at this merchant, visit PayPal settings."</string>
    <string name="paypal_checkout_billing_agreements_use_balance_for_this_transaction">Use Balance for this transaction</string>
    <string name="paypal_checkout_blackhandbag_dont_translate">Black handbag</string>
    <string name="paypal_checkout_bnpl_pay_over_time">Pay over time</string>
    <string name="paypal_checkout_brownhat_dont_translate">Brown hat</string>
    <string name="paypal_checkout_buf_backup_funding">"If your PayPal balance doesn't cover the total purchase amount, we'll charge the rest to %1$s"</string>
    <string name="paypal_checkout_buf_short">Backup: %1$s</string>
    <string name="paypal_checkout_buf_statement">"If this doesn't work we'll use %1$s"</string>
    <string name="paypal_checkout_button_session_id">Button Session ID:</string>
    <string name="paypal_checkout_buyNowPayLater">Buy now and pay in 14 days.</string>
    <string name="paypal_checkout_buyer_id">Buyer ID</string>
    <string name="paypal_checkout_cancel">Cancel</string>
    <string name="paypal_checkout_card_number">Card number</string>
    <string name="paypal_checkout_card_number_error">Enter the complete card number</string>
    <string name="paypal_checkout_cart_summary_main_screen_dont_translate">Cart Summary / Main Screen</string>
    <string name="paypal_checkout_change_address_popup">Please choose another shipping address</string>
    <string name="paypal_checkout_changing_rates">We’ll protect you from changing rates and prices. Continue now to lock in this rate and price for 72 hours.</string>
    <string name="paypal_checkout_checking">Checking</string>
    <string name="paypal_checkout_checkout_story_text_phase_1">Sending you back to the merchant to complete the purchase…</string>
    <string name="paypal_checkout_checkout_story_text_phase_1_paynow">Your purchase is complete. Sending you back to the merchant…</string>
    <string name="paypal_checkout_checkout_story_text_phase_2">You’re all set…</string>
    <string name="paypal_checkout_choose_a_new_address">Choose a new address</string>
    <string name="paypal_checkout_choose_a_pick_up_method">Choose a pickup method</string>
    <string name="paypal_checkout_choose_different_shipping_address">Choose a different shipping address</string>
    <string name="paypal_checkout_choose_new_pickup_method">Choose a new pickup method</string>
    <string name="paypal_checkout_choose_new_shipping_method">Choose a new shipping method</string>
    <string name="paypal_checkout_choose_shipping_method">Choose a shipping method</string>
    <string name="paypal_checkout_city">City</string>
    <string name="paypal_checkout_client_id_import_success_dont_translate">client id: Import Success</string>
    <string name="paypal_checkout_close">Close</string>
    <string name="paypal_checkout_complete_purchase">" You'll be able to review your order before you complete your purchase. "</string>
    <string name="paypal_checkout_complete_purchase_order">Complete Order</string>
    <string name="paypal_checkout_continue_to_application">Continue to Application</string>
    <string name="paypal_checkout_continuestring">Continue</string>
    <string name="paypal_checkout_conversion_72">Most merchants process payments right away, but delays do happen. If the merchant takes longer than 72 hours to complete your purchase, we’ll use the PayPal rate that applies at that time. This could change the total amounts you’ll pay.</string>
    <string name="paypal_checkout_conversion_back_arrow_image">Conversion Back Arrow Image</string>
    <string name="paypal_checkout_conversion_circle_image">Conversion Circle Image</string>
    <string name="paypal_checkout_converstion_72_hour_protection_disclaimer">We’ll protect you from changing rates for 72 hours. After that, the amount you’ll pay could change.</string>
    <string name="paypal_checkout_convert_with_card_issuer">Convert with card issuer</string>
    <string name="paypal_checkout_convert_with_card_issuer_a">"Total before card issuer's conversion:"</string>
    <string name="paypal_checkout_country">Country</string>
    <string name="paypal_checkout_credit_text">Credit</string>
    <string name="paypal_checkout_crypto_cta_button">Sell %1$s to Continue</string>
    <string name="paypal_checkout_crypto_cta_button_text">Sell %1$s to Continue</string>
    <string name="paypal_checkout_crypto_exchange_rate">"Exchange rate: "</string>
    <string name="paypal_checkout_crypto_got_it_button">Got it</string>
    <string name="paypal_checkout_crypto_refunds_details_text">Refunds are issued to your PayPal balance in the primary account currency, not in crypto.</string>
    <string name="paypal_checkout_crypto_refunds_details_title">Refunds go to your PayPal balance</string>
    <string name="paypal_checkout_crypto_sales_details_text">You’ll receive regular account statements to help you report gains or losses on your taxes.</string>
    <string name="paypal_checkout_crypto_sales_details_title">Crypto sales are taxable</string>
    <string name="paypal_checkout_crypto_terms_and_conditions">Full crypto terms and conditions</string>
    <string name="paypal_checkout_csc">CSC</string>
    <string name="paypal_checkout_csc_error">Enter the correct CSC</string>
    <string name="paypal_checkout_currency_conversion">Currency Conversion</string>
    <string name="paypal_checkout_currency_conversion_spread">This rate includes a currency conversion spread.</string>
    <string name="paypal_checkout_currency_for_this_purchase">Currency for this purchase</string>
    <string name="paypal_checkout_device_id">Device ID</string>
    <string name="paypal_checkout_discount">Discount</string>
    <string name="paypal_checkout_discount_format">-%1$s</string>
    <string name="paypal_checkout_distance">Distance</string>
    <string name="paypal_checkout_dummy_balance_dont_translate">Use <b>$39.89</b> PayPal balance</string>
    <string name="paypal_checkout_dummy_error_msg_dont_translate">Your CAD balance cant be used with this bank account</string>
    <string name="paypal_checkout_ec_token">EC Token:</string>
    <string name="paypal_checkout_edit_shipping_address">Edit shipping address</string>
    <string name="paypal_checkout_enter_auth_token_here_dont_translate">Enter auth token here</string>
    <string name="paypal_checkout_enter_checkout_token_here_dont_translate">Enter checkout token here</string>
    <string name="paypal_checkout_enter_client_id_here_dont_translate">Enter client id here</string>
    <string name="paypal_checkout_environment_dont_translate">Environment</string>
    <string name="paypal_checkout_error">Error</string>
    <string name="paypal_checkout_error_address">Type an Address</string>
    <string name="paypal_checkout_error_choose_another_address">Choose another shipping address</string>
    <string name="paypal_checkout_error_city">Type a City</string>
    <string name="paypal_checkout_error_connecting_paypal">Some error has occurred</string>
    <string name="paypal_checkout_error_first_name">Type a First name</string>
    <string name="paypal_checkout_error_last_name">Type a Last name</string>
    <string name="paypal_checkout_error_merchant_cant_ship">"The merchant can't ship to this address"</string>
    <string name="paypal_checkout_error_required">%1$s is required</string>
    <string name="paypal_checkout_error_state">Type a State</string>
    <string name="paypal_checkout_error_zipcode">Type a Zip code</string>
    <string name="paypal_checkout_exp">MM/YY</string>
    <string name="paypal_checkout_exp_error">Enter the correct MM/YY</string>
    <string name="paypal_checkout_expired">Expired</string>
    <string name="paypal_checkout_field_required_prefix">This field</string>
    <string name="paypal_checkout_firebase_elmo_problem">"We're sorry, things don't appear to be working right now. Please come back and try again later."</string>
    <string name="paypal_checkout_generic_network_error">Things don’t appear to be working at the moment. Please try again later.</string>
    <string name="paypal_checkout_go_back_btn">Go Back</string>
    <string name="paypal_checkout_got_it">Got it</string>
    <string name="paypal_checkout_handbag_dont_translate">handbag</string>
    <string name="paypal_checkout_headline_continue">Continue on PayPal.com</string>
    <string name="paypal_checkout_headline_leave">Leave PayPal Checkout?</string>
    <string name="paypal_checkout_headline_not_right">"Something's not right."</string>
    <string name="paypal_checkout_hi">Hi,</string>
    <string name="paypal_checkout_hi_name_template">Hi, %1$s</string>
    <string name="paypal_checkout_hint_city">City</string>
    <string name="paypal_checkout_hint_state">State</string>
    <string name="paypal_checkout_import_client_id_dont_translate">Import Client Id</string>
    <string name="paypal_checkout_import_environment_dont_translate">Import Environment</string>
    <string name="paypal_checkout_import_tokens_dont_translate">Import Tokens</string>
    <string name="paypal_checkout_insurance">Insurance</string>
    <string name="paypal_checkout_item_description">Item Description:</string>
    <string name="paypal_checkout_item_description_2">Item Description:</string>
    <string name="paypal_checkout_item_price">Item price:</string>
    <string name="paypal_checkout_item_total">Item Total</string>
    <string name="paypal_checkout_jump_to_screen_dont_translate">Jump To Screen</string>
    <string name="paypal_checkout_language_problem">It looks like our web experience may not support your language.</string>
    <string name="paypal_checkout_last_name">Last Name</string>
    <string name="paypal_checkout_learn_more">Learn More</string>
    <string name="paypal_checkout_leaving_checkout">"We'll return you to the merchant site to complete checkout."</string>
    <string name="paypal_checkout_leaving_checkout_3p">"We'll return you to the merchant to complete checkout."</string>
    <string name="paypal_checkout_leaving_paypal_change_order">I want to change my order</string>
    <string name="paypal_checkout_leaving_paypal_headline">Why do you want to exit?</string>
    <string name="paypal_checkout_leaving_paypal_prefer_not_say">I prefer not to say</string>
    <string name="paypal_checkout_leaving_paypal_something_not_right">"Something's not right"</string>
    <string name="paypal_checkout_leaving_paypal_write_your_feedback">"What's wrong?"</string>
    <string name="paypal_checkout_legal">Legal Agreements</string>
    <string name="paypal_checkout_legal_agreements">Legal agreements</string>
    <string name="paypal_checkout_live_dont_translate">Live</string>
    <string name="paypal_checkout_loading_shipping_method">Loading shipping method...</string>
    <string name="paypal_checkout_logging_you_out">Logging you out...</string>
    <string name="paypal_checkout_logout">Log out</string>
    <string name="paypal_checkout_logout_headline">Are you sure you want to log out?</string>
    <string name="paypal_checkout_merchant_id">Merchant ID</string>
    <string name="paypal_checkout_more_info">More info on the merchant site</string>
    <string name="paypal_checkout_name">Name</string>
    <string name="paypal_checkout_native_add_card_full_address_button">Add new card</string>
    <string name="paypal_checkout_native_add_card_sub_title">Add a new debit, credit, or prepaid card</string>
    <string name="paypal_checkout_native_add_continue_button">Add</string>
    <string name="paypal_checkout_new_address_save">Save</string>
    <string name="paypal_checkout_new_shipping_address">Shipping Address</string>
    <string name="paypal_checkout_no">No</string>
    <string name="paypal_checkout_no_interest_fees">No interest or fees. See terms</string>
    <string name="paypal_checkout_no_interest_if_paid_in_full">No interest if paid in full in 6 months.</string>
    <string name="paypal_checkout_no_interest_or_fees_see_terms">No interest or fees. See terms</string>
    <string name="paypal_checkout_not_confirmed">Not Confirmed</string>
    <string name="paypal_checkout_ok">Ok</string>
    <string name="paypal_checkout_option_for_your_shipping_address_zip_code">Options for zip code %1$s</string>
    <string name="paypal_checkout_order_summary">Order Summary</string>
    <string name="paypal_checkout_pay_14">Pay after 14 days</string>
    <string name="paypal_checkout_pay_21">Pay after 21 days</string>
    <string name="paypal_checkout_pay_after_days">Pay after 14 Days</string>
    <string name="paypal_checkout_pay_after_days_2">Pay after 14 days</string>
    <string name="paypal_checkout_pay_later">Pay Later</string>
    <string name="paypal_checkout_pay_now">Pay Now</string>
    <string name="paypal_checkout_pay_over_time">Apply and pay over time with PayPal Credit</string>
    <string name="paypal_checkout_payment_method_rights_link">Payment Method Rights</string>
    <string name="paypal_checkout_paypal_additional_information">Additional information</string>
    <string name="paypal_checkout_paypal_address">Address</string>
    <string name="paypal_checkout_paypal_address_line_1">Address line 1</string>
    <string name="paypal_checkout_paypal_address_line_2">Address line 2</string>
    <string name="paypal_checkout_paypal_adress_number">Address, Number</string>
    <string name="paypal_checkout_paypal_apt_ste_bldg">Apt., ste., bldg</string>
    <string name="paypal_checkout_paypal_balance">PayPal balance</string>
    <string name="paypal_checkout_paypal_balance_amount_dont_translate">%1$s%2$s</string>
    <string name="paypal_checkout_paypal_barangay_district">Barangay / District</string>
    <string name="paypal_checkout_paypal_building_name_floor_room_number">Building name, floor, room number</string>
    <string name="paypal_checkout_paypal_capital_province">Capital / Province</string>
    <string name="paypal_checkout_paypal_center_population">Center of population</string>
    <string name="paypal_checkout_paypal_center_population_city_village">Center of population (city, village)</string>
    <string name="paypal_checkout_paypal_cep">CEP</string>
    <string name="paypal_checkout_paypal_city_del_post_office">City / Delivery post office</string>
    <string name="paypal_checkout_paypal_city_district">City / District</string>
    <string name="paypal_checkout_paypal_city_municipality">City / Municipality</string>
    <string name="paypal_checkout_paypal_city_neighborhood">City / Neighborhood</string>
    <string name="paypal_checkout_paypal_city_suburb">City / Suburb</string>
    <string name="paypal_checkout_paypal_city_town">City / Town</string>
    <string name="paypal_checkout_paypal_city_town_locality">City / Town / Locality</string>
    <string name="paypal_checkout_paypal_city_town_village">City / Town / Village</string>
    <string name="paypal_checkout_paypal_city_ward_town_village">City / Ward / Town / Village</string>
    <string name="paypal_checkout_paypal_commune_locality">Commune / Locality</string>
    <string name="paypal_checkout_paypal_conversion_rate">PayPal conversion rate:</string>
    <string name="paypal_checkout_paypal_conversion_rate_00_00_usd_dont_translate">PayPal conversion rate: $00.00 USD</string>
    <string name="paypal_checkout_paypal_conversion_rate_dynamic">PayPal conversion rate: %1$s</string>
    <string name="paypal_checkout_paypal_county">County</string>
    <string name="paypal_checkout_paypal_county_dublin_postal_district">County / Dublin postal district</string>
    <string name="paypal_checkout_paypal_county_state">County / State</string>
    <string name="paypal_checkout_paypal_credit_apply">"We'll take you to PayPal to apply"</string>
    <string name="paypal_checkout_paypal_delivery_post_office">Delivery post office</string>
    <string name="paypal_checkout_paypal_dep_cap_district">Department / Capital District</string>
    <string name="paypal_checkout_paypal_department">Department</string>
    <string name="paypal_checkout_paypal_district">District</string>
    <string name="paypal_checkout_paypal_district_area">District / Area</string>
    <string name="paypal_checkout_paypal_district_capital">District / Capital</string>
    <string name="paypal_checkout_paypal_district_dependency">District / Dependency</string>
    <string name="paypal_checkout_paypal_district_neighborhood">District / Neighborhood</string>
    <string name="paypal_checkout_paypal_district_parish">District / Parish</string>
    <string name="paypal_checkout_paypal_district_sub_province">District / Sub-province</string>
    <string name="paypal_checkout_paypal_emirate">Emirate</string>
    <string name="paypal_checkout_paypal_exit_button">You are almost done! Please finalize your purchase with the merchant.</string>
    <string name="paypal_checkout_paypal_judet_sector">Judet / Sector</string>
    <string name="paypal_checkout_paypal_larger_city_province">Larger city / Province</string>
    <string name="paypal_checkout_paypal_locality">Locality</string>
    <string name="paypal_checkout_paypal_logo">Pay Pal</string>
    <string name="paypal_checkout_paypal_main_village_town">Main village / Town</string>
    <string name="paypal_checkout_paypal_more_address_details">More address details</string>
    <string name="paypal_checkout_paypal_more_island_name">Island’s name</string>
    <string name="paypal_checkout_paypal_municipality">Municipality</string>
    <string name="paypal_checkout_paypal_neighborhood_quarter">Neighborhood / Quarter</string>
    <string name="paypal_checkout_paypal_new_address_name">Name</string>
    <string name="paypal_checkout_paypal_pin_code">PIN code</string>
    <string name="paypal_checkout_paypal_plot_house_number_moo_lane_street_name">Plot / house number, moo / lane and street name</string>
    <string name="paypal_checkout_paypal_po_box">PO Box</string>
    <string name="paypal_checkout_paypal_policies">PayPal Policies</string>
    <string name="paypal_checkout_paypal_post_code">Post Code</string>
    <string name="paypal_checkout_paypal_post_office_name">Post Office name</string>
    <string name="paypal_checkout_paypal_post_town">Post town</string>
    <string name="paypal_checkout_paypal_postal_canton">Canton</string>
    <string name="paypal_checkout_paypal_postal_capital_district">Capital District</string>
    <string name="paypal_checkout_paypal_postal_code">Postal Code</string>
    <string name="paypal_checkout_paypal_postal_code_department">Postal code, Department</string>
    <string name="paypal_checkout_paypal_postcode">Postcode</string>
    <string name="paypal_checkout_paypal_prefecture">Prefecture</string>
    <string name="paypal_checkout_paypal_province">Province</string>
    <string name="paypal_checkout_paypal_province_autonomous_city">Province / Autonomous city</string>
    <string name="paypal_checkout_paypal_province_municipality">Province / Municipality</string>
    <string name="paypal_checkout_paypal_province_national_district">Province / National District</string>
    <string name="paypal_checkout_paypal_province_region">Province / Region</string>
    <string name="paypal_checkout_paypal_region">Region</string>
    <string name="paypal_checkout_paypal_region_independent_city">Region / Independent city</string>
    <string name="paypal_checkout_paypal_state_capital">State / Capital</string>
    <string name="paypal_checkout_paypal_state_capital_district_federal_dependencies">State / Capital District / Federal Dependencies</string>
    <string name="paypal_checkout_paypal_state_federal_territory">State / Federal territory</string>
    <string name="paypal_checkout_paypal_state_territory">State / Territory</string>
    <string name="paypal_checkout_paypal_street_address">Street address</string>
    <string name="paypal_checkout_paypal_street_building_apartment">Street, building, apartment</string>
    <string name="paypal_checkout_paypal_street_house">Street and house number</string>
    <string name="paypal_checkout_paypal_street_name">Street name, street number</string>
    <string name="paypal_checkout_paypal_street_name_house_number">Street name and house number</string>
    <string name="paypal_checkout_paypal_street_name_house_number_subdivision">Street name, house number, subdivision</string>
    <string name="paypal_checkout_paypal_sub_district_sub_division">Sub-district / Sub-division</string>
    <string name="paypal_checkout_paypal_sub_locality">Sub-locality</string>
    <string name="paypal_checkout_paypal_subdistrict">Subdistrict</string>
    <string name="paypal_checkout_paypal_town">Town</string>
    <string name="paypal_checkout_paypal_town_city">Town / City</string>
    <string name="paypal_checkout_paypal_town_village_post_office">Town / Village / Post office</string>
    <string name="paypal_checkout_paypal_zip_code">Zip code</string>
    <string name="paypal_checkout_paypal_zone">Zone</string>
    <string name="paypal_checkout_paypal_zone_capital">Zone / Capital</string>
    <string name="paypal_checkout_pick_it_up">Pick it up</string>
    <string name="paypal_checkout_plus_shipping">Add Shipping Address</string>
    <string name="paypal_checkout_policies">Policies</string>
    <string name="paypal_checkout_policies_and_rights">PayPal policies and your rights</string>
    <string name="paypal_checkout_policy">Policy</string>
    <string name="paypal_checkout_powered_by_google">Powered by Google</string>
    <string name="paypal_checkout_preferred">Preferred</string>
    <string name="paypal_checkout_preferred_fi_instrument_button">Select as preferred payment method</string>
    <string name="paypal_checkout_preferred_fi_instrument_button_selected">Selected as preferred payment method</string>
    <string name="paypal_checkout_preferred_interaction">PREFERRED</string>
    <string name="paypal_checkout_privacy">Privacy</string>
    <string name="paypal_checkout_privacy_2">Privacy</string>
    <string name="paypal_checkout_privacy_label">Privacy</string>
    <string name="paypal_checkout_profile_link_to_legal_agreements_dont_translate">Profile Page- logout or link to legal agreements</string>
    <string name="paypal_checkout_pseudo_snackbar_onboarding_message">"You're checking out with the PayPal app"</string>
    <string name="paypal_checkout_pulling_up_digital_wallet">Pulling up your digital wallet...</string>
    <string name="paypal_checkout_quantity">Quantity:</string>
    <string name="paypal_checkout_rate_lock_72_hour">Rate locked for 72 hours.</string>
    <string name="paypal_checkout_rate_prot_body_bold_portion">"If the merchant takes longer than 72 hours to complete your purchase, we'll use the PayPal rate that applies at that time."</string>
    <string name="paypal_checkout_rate_prot_body_end">"This could change the total amount you'll pay."</string>
    <string name="paypal_checkout_rate_prot_body_start">Most merchants process payments right away, but delays do happen.</string>
    <string name="paypal_checkout_rate_prot_info">Rate protection info</string>
    <string name="paypal_checkout_rate_prot_subtitle">Why 72 hours?</string>
    <string name="paypal_checkout_rates_and_fees">Rates and fees</string>
    <string name="paypal_checkout_recently_used">Recently used</string>
    <string name="paypal_checkout_redirected_to_web">To complete this action, continue to PayPal.com.</string>
    <string name="paypal_checkout_review_order">Review Order</string>
    <string name="paypal_checkout_sandbox_dont_translate">Sandbox</string>
    <string name="paypal_checkout_savings">Savings</string>
    <string name="paypal_checkout_sdk_app_name">PYPL Checkout</string>
    <string name="paypal_checkout_sdk_version">SDK Version</string>
    <string name="paypal_checkout_search_screen_name">%1$s %2$s</string>
    <string name="paypal_checkout_secure_easy">The secure easy way to pay</string>
    <string name="paypal_checkout_secure_easy_fixed">The secure, easy way to pay</string>
    <string name="paypal_checkout_securely_logging_you_in">Securely logging you in...</string>
    <string name="paypal_checkout_see_conversion_options">See conversion options</string>
    <string name="paypal_checkout_see_crypto_to_pay">This rate includes a currency conversion spread and refreshes until you sell your crypto.</string>
    <string name="paypal_checkout_see_crypto_to_pay_title">"We'll sell your crypto to pay"</string>
    <string name="paypal_checkout_see_more">See More</string>
    <string name="paypal_checkout_select_country">Select country</string>
    <string name="paypal_checkout_selected">Selected</string>
    <string name="paypal_checkout_sell_crypto_to_pay">This rate includes a currency conversion spread and refreshes until you sell your crypto. #lRates and fees#l</string>
    <string name="paypal_checkout_selling_crypto">"You're selling:"</string>
    <string name="paypal_checkout_selling_crypto_for_cash">Now selling your crypto for cash</string>
    <string name="paypal_checkout_selling_your_crypto">Now selling your crypto for cash</string>
    <string name="paypal_checkout_sending_back_to_merchant">Sending you back to merchant to check out…</string>
    <string name="paypal_checkout_sherman_dont_translate">Sherman</string>
    <string name="paypal_checkout_shermango_dont_translate">Shermango</string>
    <string name="paypal_checkout_ship_it">Ship it</string>
    <string name="paypal_checkout_ship_to">Ship to:</string>
    <string name="paypal_checkout_shipping_address">Choose a shipping address</string>
    <string name="paypal_checkout_shipping_address_hint">Shipping address</string>
    <string name="paypal_checkout_shipping_address_matches">List of potential matches shown</string>
    <string name="paypal_checkout_shipping_choose_way_to_get_it">Choose a way to get it</string>
    <string name="paypal_checkout_shipping_discount">Shipping Discount</string>
    <string name="paypal_checkout_shipping_handling">Shipping and handling</string>
    <string name="paypal_checkout_shipping_redirect">"To add a new shipping address, you'll need to complete your purchase on PayPal.com."</string>
    <string name="paypal_checkout_shipping_redirect_dialog">"We'll redirect you to PayPal checkout in your browser to complete this task."</string>
    <string name="paypal_checkout_shipping_redirect_ok">Open browser</string>
    <string name="paypal_checkout_smart_payment_button_label_buy_now">Buy Now</string>
    <string name="paypal_checkout_smart_payment_button_label_checkout">Checkout</string>
    <string name="paypal_checkout_smart_payment_button_label_pay">Pay with</string>
    <string name="paypal_checkout_smart_payment_button_label_pay_later">Pay Later</string>
    <string name="paypal_checkout_sorry_one_touch">Sorry we could not log you in with one touch</string>
    <string name="paypal_checkout_stage_dont_translate">Stage</string>
    <string name="paypal_checkout_start_typing">Start typing your shipping address</string>
    <string name="paypal_checkout_state">State</string>
    <string name="paypal_checkout_sub_total">Sub Total</string>
    <string name="paypal_checkout_subject_to_credit_approval">Subject to credit approval. See terms</string>
    <string name="paypal_checkout_switch_to_web_checkout">Switch to Web Checkout</string>
    <string name="paypal_checkout_tax">Tax</string>
    <string name="paypal_checkout_terms">Terms</string>
    <string name="paypal_checkout_terms_2">Terms</string>
    <string name="paypal_checkout_testcost1_dont_translate">1 x £95.00</string>
    <string name="paypal_checkout_testcost2_dont_translate">1 x £20.00</string>
    <string name="paypal_checkout_testcost_dont_translate">5 x £10.00</string>
    <string name="paypal_checkout_testhat_dont_translate">testhat</string>
    <string name="paypal_checkout_the_merchant_cannot_ship_to_this_address_choose_different_one">"The merchant can't ship to this address. Choose a different address."</string>
    <string name="paypal_checkout_this_method_is_not_available_choose_different_one">"This method isn't available. Choose a different method."</string>
    <string name="paypal_checkout_this_rate_could_change_72">This rate could change.</string>
    <string name="paypal_checkout_three_ds_add_card_error_desc">We couldn’t confirm that you’re the owner of this card. Contact the issuer if you’re unable to use it.</string>
    <string name="paypal_checkout_three_ds_add_card_error_header">We couldn’t link your card</string>
    <string name="paypal_checkout_three_ds_cta_error_desc">We couldn’t confirm that you’re the owner of this card. Contact the card issuer or link another one.</string>
    <string name="paypal_checkout_three_ds_cta_error_header">We had to remove %1$s …%2$s from your account</string>
    <string name="paypal_checkout_token_import_success_dont_translate">Token import success</string>
    <string name="paypal_checkout_total">Total</string>
    <string name="paypal_checkout_total_before_conversion_dynamic">"Total before conversion: "</string>
    <string name="paypal_checkout_transaction_details">Transaction Details</string>
    <string name="paypal_checkout_type_address">address</string>
    <string name="paypal_checkout_unavailable">Unavailable</string>
    <string name="paypal_checkout_unconfirmed_button">Try again</string>
    <string name="paypal_checkout_unconfirmed_desc">To use your linked card …%1$s, try confirming again or contact your card issuer.</string>
    <string name="paypal_checkout_unconfirmed_header">We still need to confirm your identity</string>
    <string name="paypal_checkout_update_card">Update Card</string>
    <string name="paypal_checkout_upper_conversion_text">"You'll pay in %1$s using the card issuer's conversion rate."</string>
    <string name="paypal_checkout_usd">USD</string>
    <string name="paypal_checkout_use">Use</string>
    <string name="paypal_checkout_use_card_issuer_s_conversion_rate">"Use Card Issuer's Conversion Rate"</string>
    <string name="paypal_checkout_use_currency">Use</string>
    <string name="paypal_checkout_use_paypal_balance">Use %1$s PayPal balance</string>
    <string name="paypal_checkout_use_paypal_exchange_rate">"Use PayPal's Exchange Rate"</string>
    <string name="paypal_checkout_use_with_format">Use %1$s</string>
    <string name="paypal_checkout_user_agreement_cta_complete_order">Agree &amp; Complete Order</string>
    <string name="paypal_checkout_user_agreement_cta_review_order">Agree &amp; Review Order</string>
    <string name="paypal_checkout_user_agreement_legal_text">By continuing, you agree to our updated #lTerms and Conditions.#l</string>
    <string name="paypal_checkout_user_profile_icon">Profile</string>
    <string name="paypal_checkout_user_profile_username">%1$s %2$s</string>
    <string name="paypal_checkout_view_payment_rights">View your payment method rights</string>
    <string name="paypal_checkout_view_rights">View payment method rights &amp; policies</string>
    <string name="paypal_checkout_wallet">Wallet</string>
    <string name="paypal_checkout_we_sell_your_crypto_to_pay_details_text_one">If you don’t complete the purchase, the money from your crypto sale will go into your PayPal balance.</string>
    <string name="paypal_checkout_we_sell_your_crypto_to_pay_details_text_two">If the merchant adds tax or shipping later, we’ll try covering the cost with your PayPal balance first and then your backup payment method.</string>
    <string name="paypal_checkout_welcome_to_paypal">Welcome to PayPal Checkout...</string>
    <string name="paypal_checkout_well_debit">"We'll debit your bank on"</string>
    <string name="paypal_checkout_why_72">Why 72 hours?</string>
    <string name="paypal_checkout_will_debit_on">"We'll debit your bank on"</string>
    <string name="paypal_checkout_yes">Yes</string>
    <string name="paypal_checkout_you_agree">You agree to terms</string>
    <string name="paypal_checkout_you_ll_pay">"You'll pay:"</string>
    <string name="paypal_checkout_you_ll_pay2">"You'll pay"</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_paypal_credit">"Your %1$s PayPal balance can't be used with PayPal Credit."</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_store_cash">"Your %1$s PayPal balance can't be used with Store Cash."</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_this_bank_account">"Your %1$s PayPal balance can't be used with this bank account."</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_this_credit_card">"Your %1$s PayPal balance can't be used with this credit card."</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_this_debit_card">"Your %1$s PayPal balance can't be used with this debit card."</string>
    <string name="paypal_checkout_your_balance_can_not_be_used_with_your_balance">"Your %1$s PayPal balance can't be used with your %2$s PayPal balance."</string>
    <string name="paypal_checkout_zip_code">Zip code</string>
    <string name="paypal_checkout_zip_code_error">Enter the correct Zip Code</string>
    <string name="paypal_logo">Pay Pal</string>
    <string name="paypal_overcapture_collapsed_text">You authorize up to %1$s</string>
    <string name="paypal_overcapture_expanded_details_short_text">You authorize up to %1$s to include any additional merchant charges related to shipping, tax, or other.</string>
    <string name="place_description_your_problem">Please enter a description of the problem or upload screenshots and videos</string>
    <string name="place_enter_your_email">please enter your email</string>
    <string name="please_enter_the_verification_code">Please enter the verification code</string>
    <string name="please_enter_your_email">Please enter your email</string>
    <string name="please_enter_your_password">Please enter your password</string>
    <string name="please_import_the_application_first">Please import the application first ~</string>
    <string name="preference_copied">\"%1$s\" copied to clipboard.</string>
    <string name="preferred_fi_instrument_button">Select as preferred payment method</string>
    <string name="premium_service_agreement">Premium Service Agreement.</string>
    <string name="privacy_policy">《Privacy Policy》</string>
    <string name="privacy_policy_btn">Privacy Policy</string>
    <string name="privacy_policy_url">https://www.aboxapps.com/privacyPolicy</string>
    <string name="processing">Processing</string>
    <string name="project_id">abox-37e50</string>
    <string name="ps_image">PS Image</string>
    <string name="purchase_abox_premium">Purchase Voice Premium</string>
    <string name="purchase_btn">%s To Purchase</string>
    <string name="questions_and_suggestions">Questions and Suggestions:</string>
    <string name="real_time_voice_change">Real-time Voice Change</string>
    <string name="real_time_voice_changer">Real-time Voice Changer</string>
    <string name="recommend_tips">Premium Service New User Limited-Time Super Low Price</string>
    <string name="recommended_applications">Recommended applications:</string>
    <string name="recording">Recording</string>
    <string name="recording_modulation">Recording Modulation</string>
    <string name="recording_will_end_seconds">Recording will end %d seconds</string>
    <string name="reject_audio_permission_tip">Please go to the settings page to grant Voice microphone permission in order to continue using.</string>
    <string name="release_to_change_voice">Release to change voice</string>
    <string name="renew">Renew</string>
    <string name="report_content">Please describe in detail the problems or suggestions you encountered. If the function is abnormal, uploading screenshots can solve the problem more quickly</string>
    <string name="report_email">Please enter your email</string>
    <string name="report_screenshot_or_video">Upload Screenshots:</string>
    <string name="resend_code">RESEND CODE</string>
    <string name="sandy">AI Girl</string>
    <string name="save">Save</string>
    <string name="save_succeed">Save Succeed</string>
    <string name="saved_recordings">Saved recordings</string>
    <string name="search_menu_title">Search</string>
    <string name="search_screen_name">%1$s %2$s</string>
    <string name="secured_checkout">SECURE CHECKOUT</string>
    <string name="select">Select</string>
    <string name="select_payment_method">Select Payment Method</string>
    <string name="select_premium_plan">Select Premium Plan</string>
    <string name="select_voice">Select Voice</string>
    <string name="select_voice_changing_app">Select Voice-Changing APP</string>
    <string name="set_password_tip">Please enter a password of 6 digits or more</string>
    <string name="settings">Settings</string>
    <string name="share">Share</string>
    <string name="shiloh">Shiloh</string>
    <string name="sign_in">Sign In</string>
    <string name="sign_up">Sign Up</string>
    <string name="singe_select_challenge_info">"Hello, your online payment is being secured using &lt;CardNetwork>.
Please enter a secure code sent from YourBank."</string>
    <string name="some_import_failed">Some app failed to import</string>
    <string name="sorry_no_apps_to_import">Sorry,no apps to import!</string>
    <string name="split_flag">|</string>
    <string name="ss_challengeinfo_lable">Where can we send it?</string>
    <string name="start_voice_changing">Start Voice-Changing！</string>
    <string name="start_voice_changing_btn">Start Voice Changing</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="step_1">Step 1 :</string>
    <string name="step_1_select_voice">Step1: Select Voice</string>
    <string name="step_1_subtitle">Enter the task manager page. Click on the Voice icon.</string>
    <string name="step_2">Step 2 :</string>
    <string name="step_2_select_app">Step2: Select Voice-Changing APP</string>
    <string name="step_2_subtitle">"Click 'Lock this app' to complete locking Voice in the task manager."</string>
    <string name="stop_playing_and_then_switch_the_voice">Stop playing and then switch the voice</string>
    <string name="submit">Submit</string>
    <string name="summary_collapsed_preference_list">%1$s, %2$s</string>
    <string name="telegram">Telegram</string>
    <string name="terms_of_service">《Terms of Service》</string>
    <string name="terms_of_service_btn">Terms of Service</string>
    <string name="terms_of_service_url">https://www.aboxapps.com/termsofService</string>
    <string name="textview">Great! We have sent you a text message with a secure code to your registered phone</string>
    <string name="there_are_no_current_order_records">There are no current order records</string>
    <string name="three_ds_transaction_error_no_fi">"Your card issuer wasn't able to confirm your card. Add a new card to continue."</string>
    <string name="three_ds_transaction_error_with_fi">"Your card issuer wasn't able to confirm your card. Choose another way to pay."</string>
    <string name="toast_message">Toast message</string>
    <string name="toolbar_title">SECURE CHECKOUT</string>
    <string name="trial_for_new_users_only">Trial for new users only</string>
    <string name="try_it_free">Try it free</string>
    <string name="unlock_all_voice_packs">Unlock all Voice Packs</string>
    <string name="unlock_ambient_sounds">UnLock Ambient sounds</string>
    <string name="unlock_premium_voice_effects">Unlock Premium Voice Effects</string>
    <string name="update">Update</string>
    <string name="update_detected">Update detected</string>
    <string name="upgrade_to_premium">Upgrade to Premium</string>
    <string name="upload_feedback_success">Submit feedback successfully</string>
    <string name="use_vip_voice_tip">Apologies. the %s voice effect is exclusive to Premium members and temporarily unavailable for use.</string>
    <string name="user_profile_icon">Profile</string>
    <string name="v7_preference_off">OFF</string>
    <string name="v7_preference_on">ON</string>
    <string name="verification_code">Verification code：</string>
    <string name="verify">VERIFY</string>
    <string name="verify_by_phone">Verify by Phone</string>
    <string name="vlite_server_process_name">:server</string>
    <string name="voice_alice_name">AI Female</string>
    <string name="voice_change_guide">Voice Change Guide</string>
    <string name="voice_change_guide_url">https://aboxapps.com/VoiceChangeGuide</string>
    <string name="voice_changing_app">A real-time Voice Changing App</string>
    <string name="voice_des1">Voice supports voice modulation for most social and real-time communication apps.</string>
    <string name="voice_des2">Feel free to try voice modulation with other apps you have in mind.</string>
    <string name="voice_des3">If you encounter any issues within Voice, please provide feedback to the official <font color="#C44BF1">Voice team.</font></string>
    <string name="voice_effect_initiated">Voice effect initiated!</string>
    <string name="voice_oliver_name">AI Boy</string>
    <string name="voice_preview">Voice preview</string>
    <string name="voice_transform_tip">Voice transformation in progress</string>
    <string name="wait_init_tip">Please wait for the initialization to complete.</string>
    <string name="why_do_i_need_to_log_in_to_my_account_again">Why do I need to log in to my account again?</string>
    <string name="xpopup_cancel">Cancel</string>
    <string name="xpopup_image_not_exist">Image not exist</string>
    <string name="xpopup_ok">OK</string>
    <string name="xpopup_save">save</string>
    <string name="xpopup_saved_fail">Image save failed</string>
    <string name="xpopup_saved_to_gallery">Saved to album</string>
    <string name="yaqoob">AI Male</string>
    <string name="youtube">Youtube</string>
</resources>
