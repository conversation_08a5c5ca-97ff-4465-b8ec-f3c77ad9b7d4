.class final Lcom/abox/apps/activitys/OrderHistoryActivity$Application;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/abox/apps/activitys/OrderHistoryActivity;->onTransact()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/CoroutineScope;",
        "Lkotlin/coroutines/Continuation<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u008a@"
    }
    d2 = {
        "<anonymous>",
        "",
        "Lkotlinx/coroutines/CoroutineScope;"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/coroutines/jvm/internal/DebugMetadata;
    c = "com.abox.apps.activitys.OrderHistoryActivity$onAfterViews$4"
    f = "OrderHistoryActivity.kt"
    i = {}
    l = {
        0x5e,
        0x5f
    }
    m = "invokeSuspend"
    n = {}
    s = {}
.end annotation


# instance fields
.field getDefaultImpl:I

.field final synthetic onTransact:Lcom/abox/apps/activitys/OrderHistoryActivity;


# direct methods
.method constructor <init>(Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/abox/apps/activitys/OrderHistoryActivity;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lcom/abox/apps/activitys/OrderHistoryActivity$Application;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->onTransact:Lcom/abox/apps/activitys/OrderHistoryActivity;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/Continuation;)V

    return-void
.end method


# virtual methods
.method public final asInterface(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlinx/coroutines/CoroutineScope;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/CoroutineScope;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lo/cbw;
    .end annotation

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;

    sget-object p2, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/Continuation;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/Continuation<",
            "*>;)",
            "Lkotlin/coroutines/Continuation<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lo/cbz;
    .end annotation

    new-instance p1, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;

    iget-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->onTransact:Lcom/abox/apps/activitys/OrderHistoryActivity;

    invoke-direct {p1, v0, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;-><init>(Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V

    return-object p1
.end method

.method public synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lkotlinx/coroutines/CoroutineScope;

    check-cast p2, Lkotlin/coroutines/Continuation;

    invoke-virtual {p0, p1, p2}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->asInterface(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbw;
    .end annotation

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->getCOROUTINE_SUSPENDED()Ljava/lang/Object;

    move-result-object v0

    iget v1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->getDefaultImpl:I

    const/4 v2, 0x2

    const/4 v3, 0x1

    if-eqz v1, :cond_2

    if-eq v1, v3, :cond_1

    if-ne v1, v2, :cond_0

    invoke-static {p1}, Lkotlin/ResultKt;->throwOnFailure(Ljava/lang/Object;)V

    goto :goto_1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    invoke-static {p1}, Lkotlin/ResultKt;->throwOnFailure(Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    invoke-static {p1}, Lkotlin/ResultKt;->throwOnFailure(Ljava/lang/Object;)V

    sget-object p1, Lo/ObjectStreamConstants;->getDefaultImpl:Lo/ObjectStreamConstants;

    invoke-virtual {p1}, Lo/ObjectStreamConstants;->onTransact()Lo/ObjectStreamException;

    move-result-object p1

    iput v3, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->getDefaultImpl:I

    invoke-interface {p1, p0}, Lo/ObjectStreamException;->setDefaultImpl(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_3

    return-object v0

    :cond_3
    :goto_0
    check-cast p1, Lcom/abox/apps/model/BaseResponse;

    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getMain()Lkotlinx/coroutines/MainCoroutineDispatcher;

    move-result-object v1

    new-instance v3, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;

    iget-object v4, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->onTransact:Lcom/abox/apps/activitys/OrderHistoryActivity;

    const/4 v5, 0x0

    invoke-direct {v3, p1, v4, v5}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application$5;-><init>(Lcom/abox/apps/model/BaseResponse;Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V

    iput v2, p0, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;->getDefaultImpl:I

    invoke-static {v1, v3, p0}, Lkotlinx/coroutines/BuildersKt;->withContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;

    move-result-object p1

    if-ne p1, v0, :cond_4

    return-object v0

    :cond_4
    :goto_1
    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1
.end method
