<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="drawable" name="$applovin_ic_mute_to_unmute__0">qd.xml</item>
    <item type="drawable" name="$applovin_ic_mute_to_unmute__1">afd.xml</item>
    <item type="drawable" name="$applovin_ic_mute_to_unmute__2">ans.xml</item>
    <item type="drawable" name="$applovin_ic_mute_to_unmute__3">gq.xml</item>
    <item type="drawable" name="$applovin_ic_mute_to_unmute__4">qm.xml</item>
    <item type="drawable" name="$applovin_ic_unmute_to_mute__0">nk.xml</item>
    <item type="drawable" name="$applovin_ic_unmute_to_mute__1">ft.xml</item>
    <item type="drawable" name="$applovin_ic_unmute_to_mute__2">amt.xml</item>
    <item type="drawable" name="$applovin_ic_unmute_to_mute__3">aen.xml</item>
    <item type="drawable" name="$applovin_ic_unmute_to_mute__4">pe.xml</item>
    <item type="drawable" name="$avd_hide_password__0">aez.xml</item>
    <item type="drawable" name="$avd_hide_password__1">aev.xml</item>
    <item type="drawable" name="$avd_hide_password__2">ank.xml</item>
    <item type="drawable" name="$avd_show_password__0">li.xml</item>
    <item type="drawable" name="$avd_show_password__1">l.xml</item>
    <item type="drawable" name="$avd_show_password__2">ld.xml</item>
    <item type="drawable" name="$icon_more_round__0">mk.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__0">lx.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__1">mb.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__10">qk.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__11">gi.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__12">ang.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__13">aeg.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__14">lj.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__15">bi.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__16">aik.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__17">sq.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__18">aaz.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__19">lf.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__2">lv.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__20">qi.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__3">ls.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__4">lw.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__5">lt.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__6">lu.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__7">lq.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__8">lo.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_ky__9">lp.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__0">jn.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__1">jl.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__10">ahz.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__11">ru.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__12">kk.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__13">jv.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__14">a.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__15">nc.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__16">tm.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__17">akn.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__18">anp.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__19">gk.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__2">jm.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__20">ahm.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__21">rw.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__22">kn.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__23">kd.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__24">f.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__25">my.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__26">tl.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__27">akf.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__28">anw.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__29">gh.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__3">jo.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__30">ahu.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__31">se.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__32">ke.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__33">jq.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__34">j.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__35">nj.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__36">tp.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__37">akh.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__38">anv.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__39">gd.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__4">jk.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__40">ahr.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__41">sb.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__5">ji.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__6">jh.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__7">jj.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__8">jg.xml</item>
    <item type="drawable" name="$paypal_checkout_flag_pm__9">jf.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_amex__0">ahf.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_amex__1">ahc.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_applynoshadow__0">aki.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__0">bg.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__1">jc.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__2">rx.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__3">ahh.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__4">bl.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_blue_bg__5">jb.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_boa__0">st.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_capitolone__0">agm.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_card_empty_state__0">jx.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_card_empty_state__1">ju.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_cb__0">hd.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_cb__1">tr.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_chase__0">iq.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_diners_mini__0">qn.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_diners_mini__1">afo.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_discover__0">rm.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_discover__1">rp.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_ebay_mastercard__0">gc.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_hiper__0">bh.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_maestro__0">cl.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_mc__0">adm.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_payment_card_gray_bgd_vector__0">amy.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_payment_card_gray_bgd_vector__1">amv.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_pp_smart_connect__0">cp.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_pp_smart_connect__1">cn.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_ppmc__0">cg.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_ppmc__1">ce.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_store_exit_animation__0">cz.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_store_exit_animation__1">da.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_store_exit_crypto__0">abo.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_store_exit_crypto__1">la.xml</item>
    <item type="drawable" name="$paypal_checkout_ic_updatedvisa__0">aew.xml</item>
    <item type="drawable" name="$paypal_checkout_three_ds_anim__0">gp.xml</item>
    <item type="drawable" name="$paypal_checkout_three_ds_anim__1">gw.xml</item>
    <item type="drawable" name="$paypal_checkout_three_ds_anim__2">v.xml</item>
    <item type="drawable" name="abc_action_bar_item_background_material">afz.xml</item>
    <item type="drawable" name="abc_btn_borderless_material">ahb.xml</item>
    <item type="drawable" name="abc_btn_check_material">aho.xml</item>
    <item type="drawable" name="abc_btn_check_material_anim">acn.xml</item>
    <item type="drawable" name="abc_btn_colored_material">anj.xml</item>
    <item type="drawable" name="abc_btn_default_mtrl_shape">rd.xml</item>
    <item type="drawable" name="abc_btn_radio_material">mg.xml</item>
    <item type="drawable" name="abc_btn_radio_material_anim">aay.xml</item>
    <item type="drawable" name="abc_cab_background_internal_bg">mq.xml</item>
    <item type="drawable" name="abc_cab_background_top_material">agd.xml</item>
    <item type="drawable" name="abc_control_background_material">qx.xml</item>
    <item type="drawable" name="abc_dialog_material_background">ss.xml</item>
    <item type="drawable" name="abc_edit_text_material">th.xml</item>
    <item type="drawable" name="abc_ic_ab_back_material">aeo.xml</item>
    <item type="drawable" name="abc_ic_arrow_drop_right_black_24dp">gg.xml</item>
    <item type="drawable" name="abc_ic_clear_material">qr.xml</item>
    <item type="drawable" name="abc_ic_go_search_api_material">as.xml</item>
    <item type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha">ip.xml</item>
    <item type="drawable" name="abc_ic_menu_cut_mtrl_alpha">bb.xml</item>
    <item type="drawable" name="abc_ic_menu_overflow_material">aht.xml</item>
    <item type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha">aff.xml</item>
    <item type="drawable" name="abc_ic_menu_selectall_mtrl_alpha">ad.xml</item>
    <item type="drawable" name="abc_ic_menu_share_mtrl_alpha">id.xml</item>
    <item type="drawable" name="abc_ic_search_api_material">lm.xml</item>
    <item type="drawable" name="abc_ic_voice_search_api_material">ms.xml</item>
    <item type="drawable" name="abc_item_background_holo_dark">afc.xml</item>
    <item type="drawable" name="abc_item_background_holo_light">aju.xml</item>
    <item type="drawable" name="abc_list_divider_material">ac.xml</item>
    <item type="drawable" name="abc_list_selector_background_transition_holo_dark">ant.xml</item>
    <item type="drawable" name="abc_list_selector_background_transition_holo_light">sd.xml</item>
    <item type="drawable" name="abc_list_selector_holo_dark">hc.xml</item>
    <item type="drawable" name="abc_list_selector_holo_light">agi.xml</item>
    <item type="drawable" name="abc_ratingbar_indicator_material">rr.xml</item>
    <item type="drawable" name="abc_ratingbar_material">mt.xml</item>
    <item type="drawable" name="abc_ratingbar_small_material">le.xml</item>
    <item type="drawable" name="abc_seekbar_thumb_material">h.xml</item>
    <item type="drawable" name="abc_seekbar_tick_mark_material">agw.xml</item>
    <item type="drawable" name="abc_seekbar_track_material">js.xml</item>
    <item type="drawable" name="abc_spinner_textfield_background_material">gs.xml</item>
    <item type="drawable" name="abc_star_black_48dp">hs.xml</item>
    <item type="drawable" name="abc_star_half_black_48dp">aba.xml</item>
    <item type="drawable" name="abc_switch_thumb_material">n.xml</item>
    <item type="drawable" name="abc_tab_indicator_material">aj.xml</item>
    <item type="drawable" name="abc_text_cursor_material">anc.xml</item>
    <item type="drawable" name="abc_textfield_search_material">pj.xml</item>
    <item type="drawable" name="abc_vector_test">kf.xml</item>
    <item type="drawable" name="ad_close_icon">ks.xml</item>
    <item type="drawable" name="add_normal">bz.xml</item>
    <item type="drawable" name="add_selected">mj.xml</item>
    <item type="drawable" name="al_exo_controls_fastforward">@drawable/applovin_exo_icon_fastforward</item>
    <item type="drawable" name="al_exo_controls_fullscreen_enter">@drawable/applovin_exo_icon_fullscreen_enter</item>
    <item type="drawable" name="al_exo_controls_fullscreen_exit">@drawable/applovin_exo_icon_fullscreen_exit</item>
    <item type="drawable" name="al_exo_controls_next">@drawable/applovin_exo_icon_next</item>
    <item type="drawable" name="al_exo_controls_pause">@drawable/applovin_exo_icon_pause</item>
    <item type="drawable" name="al_exo_controls_play">@drawable/applovin_exo_icon_play</item>
    <item type="drawable" name="al_exo_controls_previous">@drawable/applovin_exo_icon_previous</item>
    <item type="drawable" name="al_exo_controls_repeat_all">@drawable/applovin_exo_icon_repeat_all</item>
    <item type="drawable" name="al_exo_controls_repeat_off">@drawable/applovin_exo_icon_repeat_off</item>
    <item type="drawable" name="al_exo_controls_repeat_one">@drawable/applovin_exo_icon_repeat_one</item>
    <item type="drawable" name="al_exo_controls_rewind">@drawable/applovin_exo_icon_rewind</item>
    <item type="drawable" name="al_exo_controls_shuffle_off">@drawable/applovin_exo_icon_shuffle_off</item>
    <item type="drawable" name="al_exo_controls_shuffle_on">@drawable/applovin_exo_icon_shuffle_on</item>
    <item type="drawable" name="al_exo_controls_vr">@drawable/applovin_exo_icon_vr</item>
    <item type="drawable" name="al_exo_notification_fastforward">@drawable/applovin_exo_icon_fastforward</item>
    <item type="drawable" name="al_exo_notification_next">@drawable/applovin_exo_icon_next</item>
    <item type="drawable" name="al_exo_notification_pause">@drawable/applovin_exo_icon_pause</item>
    <item type="drawable" name="al_exo_notification_play">@drawable/applovin_exo_icon_play</item>
    <item type="drawable" name="al_exo_notification_previous">@drawable/applovin_exo_icon_previous</item>
    <item type="drawable" name="al_exo_notification_rewind">@drawable/applovin_exo_icon_rewind</item>
    <item type="drawable" name="al_exo_notification_small_icon">@drawable/applovin_exo_icon_circular_play</item>
    <item type="drawable" name="al_exo_notification_stop">@drawable/applovin_exo_icon_stop</item>
    <item type="drawable" name="al_exo_styled_controls_audiotrack">@drawable/applovin_exo_ic_audiotrack</item>
    <item type="drawable" name="al_exo_styled_controls_check">@drawable/applovin_exo_ic_check</item>
    <item type="drawable" name="al_exo_styled_controls_fastforward">@drawable/applovin_exo_ic_forward</item>
    <item type="drawable" name="al_exo_styled_controls_fullscreen_enter">@drawable/applovin_exo_ic_fullscreen_enter</item>
    <item type="drawable" name="al_exo_styled_controls_fullscreen_exit">@drawable/applovin_exo_ic_fullscreen_exit</item>
    <item type="drawable" name="al_exo_styled_controls_next">@drawable/applovin_exo_ic_skip_next</item>
    <item type="drawable" name="al_exo_styled_controls_overflow_hide">@drawable/applovin_exo_ic_chevron_left</item>
    <item type="drawable" name="al_exo_styled_controls_overflow_show">@drawable/applovin_exo_ic_chevron_right</item>
    <item type="drawable" name="al_exo_styled_controls_pause">@drawable/applovin_exo_ic_pause_circle_filled</item>
    <item type="drawable" name="al_exo_styled_controls_play">@drawable/applovin_exo_ic_play_circle_filled</item>
    <item type="drawable" name="al_exo_styled_controls_previous">@drawable/applovin_exo_ic_skip_previous</item>
    <item type="drawable" name="al_exo_styled_controls_repeat_all">@drawable/applovin_exo_icon_repeat_all</item>
    <item type="drawable" name="al_exo_styled_controls_repeat_off">@drawable/applovin_exo_icon_repeat_off</item>
    <item type="drawable" name="al_exo_styled_controls_repeat_one">@drawable/applovin_exo_icon_repeat_one</item>
    <item type="drawable" name="al_exo_styled_controls_rewind">@drawable/applovin_exo_ic_rewind</item>
    <item type="drawable" name="al_exo_styled_controls_settings">@drawable/applovin_exo_ic_settings</item>
    <item type="drawable" name="al_exo_styled_controls_shuffle_off">@drawable/applovin_exo_icon_shuffle_off</item>
    <item type="drawable" name="al_exo_styled_controls_shuffle_on">@drawable/applovin_exo_icon_shuffle_on</item>
    <item type="drawable" name="al_exo_styled_controls_speed">@drawable/applovin_exo_ic_speed</item>
    <item type="drawable" name="al_exo_styled_controls_subtitle_off">@drawable/applovin_exo_ic_subtitle_off</item>
    <item type="drawable" name="al_exo_styled_controls_subtitle_on">@drawable/applovin_exo_ic_subtitle_on</item>
    <item type="drawable" name="al_exo_styled_controls_vr">@drawable/applovin_exo_icon_vr</item>
    <item type="drawable" name="amp_button_bg">kw.xml</item>
    <item type="drawable" name="applovin_consent_flow_gdpr_flow_switch_thumb">afi.xml</item>
    <item type="drawable" name="applovin_consent_flow_gdpr_flow_switch_track">tc.xml</item>
    <item type="drawable" name="applovin_consent_flow_gdpr_positive_button_background">anu.xml</item>
    <item type="drawable" name="applovin_consent_flow_gdpr_rounded_background">si.xml</item>
    <item type="drawable" name="applovin_creative_debugger_report_ad_rounded_button">ajv.xml</item>
    <item type="drawable" name="applovin_exo_icon_circular_play">an</item>
    <item type="drawable" name="applovin_exo_icon_vr">aat</item>
    <item type="drawable" name="applovin_exo_rounded_rectangle">ai.xml</item>
    <item type="drawable" name="applovin_ic_mediation_placeholder">rl.xml</item>
    <item type="drawable" name="applovin_ic_mute_to_unmute">ia.xml</item>
    <item type="drawable" name="applovin_ic_privacy_icon">lh</item>
    <item type="drawable" name="applovin_ic_privacy_icon_layered_list">md.xml</item>
    <item type="drawable" name="applovin_ic_unmute_to_mute">abu.xml</item>
    <item type="drawable" name="applovin_ic_white_small">pu</item>
    <item type="drawable" name="applovin_rounded_black_background">kl.xml</item>
    <item type="drawable" name="applovin_rounded_button">ajl.xml</item>
    <item type="drawable" name="applovin_rounded_text_view_border">hf.xml</item>
    <item type="drawable" name="avd_hide_password">ahs.xml</item>
    <item type="drawable" name="avd_show_password">pf.xml</item>
    <item type="drawable" name="bg_bottom_nav">arp</item>
    <item type="drawable" name="bg_bottom_tab">aq.xml</item>
    <item type="drawable" name="bg_button_kit_disabled">aid.xml</item>
    <item type="drawable" name="bg_button_kit_primary">pt.xml</item>
    <item type="drawable" name="bg_button_kit_primary_radius_8">abj.xml</item>
    <item type="drawable" name="bg_button_kit_radius_40">ahw.xml</item>
    <item type="drawable" name="bg_corner_16_solid_white_8">ml.xml</item>
    <item type="drawable" name="bg_corner_16_stroke_white">ps.xml</item>
    <item type="drawable" name="bg_corner_8_solid_black_stroke_gray">gf.xml</item>
    <item type="drawable" name="bg_corner_8_stroke_gray">hx.xml</item>
    <item type="drawable" name="bg_corner_float_ball">aii.xml</item>
    <item type="drawable" name="bg_corners_dialog_kit">afp.xml</item>
    <item type="drawable" name="bg_corners_loading">cf.xml</item>
    <item type="drawable" name="bg_corners_search">jd.xml</item>
    <item type="drawable" name="bg_dialog_content">afb.xml</item>
    <item type="drawable" name="bg_dialog_negative">qy.xml</item>
    <item type="drawable" name="bg_hide_float">anf.xml</item>
    <item type="drawable" name="bg_import_app">qg.xml</item>
    <item type="drawable" name="bg_play">ajd.xml</item>
    <item type="drawable" name="bg_play_voice">rb.xml</item>
    <item type="drawable" name="bg_positive">aex.xml</item>
    <item type="drawable" name="bg_progress_backaground">akd.xml</item>
    <item type="drawable" name="bg_renew">mw.xml</item>
    <item type="drawable" name="btn_checkbox_checked_mtrl">aic.xml</item>
    <item type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation">ail.xml</item>
    <item type="drawable" name="btn_checkbox_unchecked_mtrl">q.xml</item>
    <item type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation">adp.xml</item>
    <item type="drawable" name="btn_radio_off_mtrl">gj.xml</item>
    <item type="drawable" name="btn_radio_off_to_on_mtrl_animation">kr.xml</item>
    <item type="drawable" name="btn_radio_on_mtrl">iy.xml</item>
    <item type="drawable" name="btn_radio_on_to_off_mtrl_animation">aei.xml</item>
    <item type="drawable" name="card_network">asn</item>
    <item type="drawable" name="color_cursor">be.xml</item>
    <item type="drawable" name="com_facebook_auth_dialog_background">abs.xml</item>
    <item type="drawable" name="com_facebook_auth_dialog_cancel_background">aft.xml</item>
    <item type="drawable" name="com_facebook_auth_dialog_header_background">hr.xml</item>
    <item type="drawable" name="com_facebook_button_background">at.xml</item>
    <item type="drawable" name="com_facebook_button_icon">ly.xml</item>
    <item type="drawable" name="com_facebook_button_like_background">gv.xml</item>
    <item type="drawable" name="com_facebook_favicon_blue">ak.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark">fx.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_focused">gu.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_dark_normal">agk.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_disabled">ni.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_light">el.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_focused">amr.xml</item>
    <item type="drawable" name="common_google_signin_btn_icon_light_normal">ala.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_dark">aet.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_focused">agn.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_dark_normal">qw.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_disabled">oy.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_light">acm.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_light_focused">cy.xml</item>
    <item type="drawable" name="common_google_signin_btn_text_light_normal">sm.xml</item>
    <item type="drawable" name="cursor_blue">g.xml</item>
    <item type="drawable" name="custom_cursor">pv.xml</item>
    <item type="drawable" name="custom_seek_bar_btn">ahd.xml</item>
    <item type="drawable" name="custom_seekbar_thumb">tn.xml</item>
    <item type="drawable" name="delete">afq.xml</item>
    <item type="drawable" name="delete_app_bg_corners_dialog_kit">mp.xml</item>
    <item type="drawable" name="design_fab_background">qs.xml</item>
    <item type="drawable" name="design_ic_visibility">ajp.xml</item>
    <item type="drawable" name="design_ic_visibility_off">s.xml</item>
    <item type="drawable" name="design_password_eye">kc.xml</item>
    <item type="drawable" name="design_snackbar_background">aby.xml</item>
    <item type="drawable" name="edit_text_border">afw.xml</item>
    <item type="drawable" name="goods_list_item_skeleton">k.xml</item>
    <item type="drawable" name="history_item_bg">akq.xml</item>
    <item type="drawable" name="home_import_app_btn">ht.xml</item>
    <item type="drawable" name="ic_arrow_down_24dp">abd.xml</item>
    <item type="drawable" name="ic_back_white">py.xml</item>
    <item type="drawable" name="ic_check_circle">hb.xml</item>
    <item type="drawable" name="ic_checkbox_n_16">aiu.xml</item>
    <item type="drawable" name="ic_checkbox_p_16">pq.xml</item>
    <item type="drawable" name="ic_checked_box">gz.xml</item>
    <item type="drawable" name="ic_clock_black_24dp">qe.xml</item>
    <item type="drawable" name="ic_close">ajq.xml</item>
    <item type="drawable" name="ic_close_24px">rj.xml</item>
    <item type="drawable" name="ic_error">pu.xml</item>
    <item type="drawable" name="ic_exit_icon">afl.xml</item>
    <item type="drawable" name="ic_float_left">nf.xml</item>
    <item type="drawable" name="ic_float_right">afk.xml</item>
    <item type="drawable" name="ic_folder">ajc.xml</item>
    <item type="drawable" name="ic_header_close">ag.xml</item>
    <item type="drawable" name="ic_keyboard_black_24dp">hu.xml</item>
    <item type="drawable" name="ic_launcher">atf</item>
    <item type="drawable" name="ic_m3_chip_check">adq.xml</item>
    <item type="drawable" name="ic_m3_chip_checked_circle">bf.xml</item>
    <item type="drawable" name="ic_m3_chip_close">hp.xml</item>
    <item type="drawable" name="ic_mtrl_checked_circle">po.xml</item>
    <item type="drawable" name="ic_mtrl_chip_checked_black">ix.xml</item>
    <item type="drawable" name="ic_mtrl_chip_checked_circle">z.xml</item>
    <item type="drawable" name="ic_mtrl_chip_close_circle">aiq.xml</item>
    <item type="drawable" name="ic_radio_button_unchecked">pk.xml</item>
    <item type="drawable" name="ic_selected">io.xml</item>
    <item type="drawable" name="ic_toolbar_back">hi.xml</item>
    <item type="drawable" name="ic_uncheck_box">fw.xml</item>
    <item type="drawable" name="ic_voice_more">bu.xml</item>
    <item type="drawable" name="ic_warning">qa.xml</item>
    <item type="drawable" name="icon_close">ahq.xml</item>
    <item type="drawable" name="icon_close_login_tips">bo.xml</item>
    <item type="drawable" name="icon_delete">cq.xml</item>
    <item type="drawable" name="icon_delete_normal">l</item>
    <item type="drawable" name="icon_delete_selected">fg</item>
    <item type="drawable" name="icon_down">je.xml</item>
    <item type="drawable" name="icon_guide">aep.xml</item>
    <item type="drawable" name="icon_import_app">nb.xml</item>
    <item type="drawable" name="icon_more_round">ann.xml</item>
    <item type="drawable" name="icon_pause">kp.xml</item>
    <item type="drawable" name="icon_play">afg.xml</item>
    <item type="drawable" name="icon_play_pause">sz.xml</item>
    <item type="drawable" name="icon_play_start">td.xml</item>
    <item type="drawable" name="icon_play_stop">anl.xml</item>
    <item type="drawable" name="icon_right_arrow">ahl.xml</item>
    <item type="drawable" name="icon_save">rs.xml</item>
    <item type="drawable" name="icon_share">qj.xml</item>
    <item type="drawable" name="label_bg">af.xml</item>
    <item type="drawable" name="login_button_blue">i.xml</item>
    <item type="drawable" name="login_guide_box_bg">gr.xml</item>
    <item type="drawable" name="logout_bg">ka.xml</item>
    <item type="drawable" name="m3_appbar_background">gt.xml</item>
    <item type="drawable" name="m3_popupmenu_background_overlay">t.xml</item>
    <item type="drawable" name="m3_radiobutton_ripple">ra.xml</item>
    <item type="drawable" name="m3_selection_control_ripple">aiv.xml</item>
    <item type="drawable" name="m3_tabs_background">cw.xml</item>
    <item type="drawable" name="m3_tabs_line_indicator">ki.xml</item>
    <item type="drawable" name="m3_tabs_rounded_line_indicator">sf.xml</item>
    <item type="drawable" name="m3_tabs_transparent_background">abb.xml</item>
    <item type="drawable" name="mail">by.xml</item>
    <item type="drawable" name="material_cursor_drawable">ap.xml</item>
    <item type="drawable" name="material_ic_calendar_black_24dp">aeq.xml</item>
    <item type="drawable" name="material_ic_clear_black_24dp">abl.xml</item>
    <item type="drawable" name="material_ic_edit_black_24dp">anb.xml</item>
    <item type="drawable" name="material_ic_keyboard_arrow_left_black_24dp">ajj.xml</item>
    <item type="drawable" name="material_ic_keyboard_arrow_next_black_24dp">@drawable/material_ic_keyboard_arrow_right_black_24dp</item>
    <item type="drawable" name="material_ic_keyboard_arrow_previous_black_24dp">@drawable/material_ic_keyboard_arrow_left_black_24dp</item>
    <item type="drawable" name="material_ic_keyboard_arrow_right_black_24dp">cj.xml</item>
    <item type="drawable" name="material_ic_menu_arrow_down_black_24dp">na.xml</item>
    <item type="drawable" name="material_ic_menu_arrow_up_black_24dp">lb.xml</item>
    <item type="drawable" name="minus">nd.xml</item>
    <item type="drawable" name="mtrl_dialog_background">ajo.xml</item>
    <item type="drawable" name="mtrl_dropdown_arrow">jt.xml</item>
    <item type="drawable" name="mtrl_ic_arrow_drop_down">re.xml</item>
    <item type="drawable" name="mtrl_ic_arrow_drop_up">ky.xml</item>
    <item type="drawable" name="mtrl_ic_cancel">rc.xml</item>
    <item type="drawable" name="mtrl_ic_error">ay.xml</item>
    <item type="drawable" name="mtrl_navigation_bar_item_background">ti.xml</item>
    <item type="drawable" name="mtrl_popupmenu_background">amw.xml</item>
    <item type="drawable" name="mtrl_popupmenu_background_overlay">agv.xml</item>
    <item type="drawable" name="mtrl_tabs_default_indicator">abi.xml</item>
    <item type="drawable" name="navigation_bg">ajk.xml</item>
    <item type="drawable" name="navigation_empty_icon">age.xml</item>
    <item type="drawable" name="notification_action_background">aib.xml</item>
    <item type="drawable" name="notification_bg">ho.xml</item>
    <item type="drawable" name="notification_bg_low">ax.xml</item>
    <item type="drawable" name="notification_icon_background">mf.xml</item>
    <item type="drawable" name="notification_template_icon_bg">#3333b5e5</item>
    <item type="drawable" name="notification_template_icon_low_bg">#0cffffff</item>
    <item type="drawable" name="notification_tile_bg">gn.xml</item>
    <item type="drawable" name="payment_method_bg">mn.xml</item>
    <item type="drawable" name="paypal_auth_cursor">pg.xml</item>
    <item type="drawable" name="paypal_checkout_action_button">av.xml</item>
    <item type="drawable" name="paypal_checkout_add_manually_arrow">jz.xml</item>
    <item type="drawable" name="paypal_checkout_address_button_background">ta.xml</item>
    <item type="drawable" name="paypal_checkout_button_dialog_shape_negative">hj.xml</item>
    <item type="drawable" name="paypal_checkout_button_dialog_shape_positive">aes.xml</item>
    <item type="drawable" name="paypal_checkout_circle">ca.xml</item>
    <item type="drawable" name="paypal_checkout_circular_shadow">ii.xml</item>
    <item type="drawable" name="paypal_checkout_cursor_blue">df.xml</item>
    <item type="drawable" name="paypal_checkout_custom_progress">sx.xml</item>
    <item type="drawable" name="paypal_checkout_custom_progress_faster">aer.xml</item>
    <item type="drawable" name="paypal_checkout_dashed_border">bc.xml</item>
    <item type="drawable" name="paypal_checkout_dialog_maker_shape">ajf.xml</item>
    <item type="drawable" name="paypal_checkout_divider">ne.xml</item>
    <item type="drawable" name="paypal_checkout_exit_icon">ro.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ad">acd.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ae">ace.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ag">aca.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ai">ach.xml</item>
    <item type="drawable" name="paypal_checkout_flag_al">acg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_am">ack.xml</item>
    <item type="drawable" name="paypal_checkout_flag_an">aci.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ao">acj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ar">acf.xml</item>
    <item type="drawable" name="paypal_checkout_flag_at">acc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_au">acb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_aw">abz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_az">abw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ba">alb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bb">akw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_be">akv.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bf">aku.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bg">aks.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bh">akt.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bi">akr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bj">akp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bm">alf.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bn">alg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bo">ald.xml</item>
    <item type="drawable" name="paypal_checkout_flag_br">alc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bs">ale.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bt">aky.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bw">akx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_by">akz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_bz">ako.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ca">di.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cd">dg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cg">dh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ch">dj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ci">dt.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ck">dr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cl">dp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cm">do.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cn">dn.xml</item>
    <item type="drawable" name="paypal_checkout_flag_co">dl.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cr">dm.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cv">dk.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cy">de.xml</item>
    <item type="drawable" name="paypal_checkout_flag_cz">dd.xml</item>
    <item type="drawable" name="paypal_checkout_flag_de">nq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_dj">nn.xml</item>
    <item type="drawable" name="paypal_checkout_flag_dk">nm.xml</item>
    <item type="drawable" name="paypal_checkout_flag_dm">no.xml</item>
    <item type="drawable" name="paypal_checkout_flag_do">nl.xml</item>
    <item type="drawable" name="paypal_checkout_flag_dz">np.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ec">aeh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ee">aee.xml</item>
    <item type="drawable" name="paypal_checkout_flag_eg">adz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_er">aec.xml</item>
    <item type="drawable" name="paypal_checkout_flag_es">aed.xml</item>
    <item type="drawable" name="paypal_checkout_flag_et">aea.xml</item>
    <item type="drawable" name="paypal_checkout_flag_eu">aeb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fi">alp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fj">aln.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fk">alq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fm">alm.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fo">all.xml</item>
    <item type="drawable" name="paypal_checkout_flag_fr">alk.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ga">fg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gb">ff.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gd">fe.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ge">fh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gf">fq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gi">fp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gl">fr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_globe">ig.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gm">fj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gn">fk.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gp">fn.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gr">fl.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gt">fm.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gw">fi.xml</item>
    <item type="drawable" name="paypal_checkout_flag_gy">fa.xml</item>
    <item type="drawable" name="paypal_checkout_flag_hk">oe.xml</item>
    <item type="drawable" name="paypal_checkout_flag_hn">oa.xml</item>
    <item type="drawable" name="paypal_checkout_flag_hr">oc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_hu">ob.xml</item>
    <item type="drawable" name="paypal_checkout_flag_id">adw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ie">adv.xml</item>
    <item type="drawable" name="paypal_checkout_flag_il">ady.xml</item>
    <item type="drawable" name="paypal_checkout_flag_in">adx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_is">adu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_it">adr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_jm">alh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_jo">alj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_jp">ali.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ke">fb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kg">fc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kh">ez.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ki">fd.xml</item>
    <item type="drawable" name="paypal_checkout_flag_km">ev.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kn">ey.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kr">ex.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kw">ew.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ky">eu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_kz">er.xml</item>
    <item type="drawable" name="paypal_checkout_flag_la">nw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_lc">nv.xml</item>
    <item type="drawable" name="paypal_checkout_flag_li">ny.xml</item>
    <item type="drawable" name="paypal_checkout_flag_lk">nx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ls">nz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_lt">ns.xml</item>
    <item type="drawable" name="paypal_checkout_flag_lu">nr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_lv">nu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ma">acw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mc">acz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_md">acx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_me">acv.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mg">act.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mh">acq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mk">acr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ml">acu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mn">acs.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mq">adi.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mr">adj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ms">adc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mt">ade.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mu">adb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mv">add.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mw">ada.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mx">acy.xml</item>
    <item type="drawable" name="paypal_checkout_flag_my">acl.xml</item>
    <item type="drawable" name="paypal_checkout_flag_mz">aco.xml</item>
    <item type="drawable" name="paypal_checkout_flag_na">aly.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nc">ama.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ne">amg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nf">amk.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ng">amh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ni">ame.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nl">amf.xml</item>
    <item type="drawable" name="paypal_checkout_flag_no">amc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_np">amd.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nr">amb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nu">alz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_nz">alx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_om">ek.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pa">ou.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pe">os.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pf">oz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pg">pa.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ph">pd.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pl">pc.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pm">pb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pn">ox.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pt">ow.xml</item>
    <item type="drawable" name="paypal_checkout_flag_pw">ov.xml</item>
    <item type="drawable" name="paypal_checkout_flag_py">oq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_qa">acp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_re">alw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ro">alt.xml</item>
    <item type="drawable" name="paypal_checkout_flag_rs">als.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ru">alr.xml</item>
    <item type="drawable" name="paypal_checkout_flag_rw">alu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sa">eb.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sb">ed.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sc">dy.xml</item>
    <item type="drawable" name="paypal_checkout_flag_se">dz.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sg">dw.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sh">dv.xml</item>
    <item type="drawable" name="paypal_checkout_flag_si">dx.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sj">eg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sk">ei.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sl">ej.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sm">ef.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sn">eh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_so">ee.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sr">ec.xml</item>
    <item type="drawable" name="paypal_checkout_flag_st">ea.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sv">ds.xml</item>
    <item type="drawable" name="paypal_checkout_flag_sz">dq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tc">oh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_td">of.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tg">og.xml</item>
    <item type="drawable" name="paypal_checkout_flag_th">oi.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tj">op.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tm">ot.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tn">or.xml</item>
    <item type="drawable" name="paypal_checkout_flag_to">on.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tr">oo.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tt">ok.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tv">ol.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tw">om.xml</item>
    <item type="drawable" name="paypal_checkout_flag_tz">oj.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ua">adk.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ug">ado.xml</item>
    <item type="drawable" name="paypal_checkout_flag_us">adl.xml</item>
    <item type="drawable" name="paypal_checkout_flag_uy">adg.xml</item>
    <item type="drawable" name="paypal_checkout_flag_va">amq.xml</item>
    <item type="drawable" name="paypal_checkout_flag_vc">amu.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ve">ams.xml</item>
    <item type="drawable" name="paypal_checkout_flag_vg">amm.xml</item>
    <item type="drawable" name="paypal_checkout_flag_vn">amp.xml</item>
    <item type="drawable" name="paypal_checkout_flag_vu">amn.xml</item>
    <item type="drawable" name="paypal_checkout_flag_wf">eo.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ws">em.xml</item>
    <item type="drawable" name="paypal_checkout_flag_ye">adh.xml</item>
    <item type="drawable" name="paypal_checkout_flag_yt">adf.xml</item>
    <item type="drawable" name="paypal_checkout_flag_za">aml.xml</item>
    <item type="drawable" name="paypal_checkout_flag_zm">ami.xml</item>
    <item type="drawable" name="paypal_checkout_flag_zw">amj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_add">ln.xml</item>
    <item type="drawable" name="paypal_checkout_ic_add_card_error">ic.xml</item>
    <item type="drawable" name="paypal_checkout_ic_address_book_icon">bs.xml</item>
    <item type="drawable" name="paypal_checkout_ic_amex">agx.xml</item>
    <item type="drawable" name="paypal_checkout_ic_amex_mini">ajb.xml</item>
    <item type="drawable" name="paypal_checkout_ic_applynoshadow">ahg.xml</item>
    <item type="drawable" name="paypal_checkout_ic_arrow_back_icon">al.xml</item>
    <item type="drawable" name="paypal_checkout_ic_attention">agh.xml</item>
    <item type="drawable" name="paypal_checkout_ic_aurore">aim.xml</item>
    <item type="drawable" name="paypal_checkout_ic_back_arrow_white">jp.xml</item>
    <item type="drawable" name="paypal_checkout_ic_balance_blue">aey.xml</item>
    <item type="drawable" name="paypal_checkout_ic_balance_blue_vector">iw.xml</item>
    <item type="drawable" name="paypal_checkout_ic_bank_dark_bg">k</item>
    <item type="drawable" name="paypal_checkout_ic_blue_bg">km.xml</item>
    <item type="drawable" name="paypal_checkout_ic_boa">w.xml</item>
    <item type="drawable" name="paypal_checkout_ic_calculator">afj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_capitolone">aia.xml</item>
    <item type="drawable" name="paypal_checkout_ic_card">ach</item>
    <item type="drawable" name="paypal_checkout_ic_card_csc">any.xml</item>
    <item type="drawable" name="paypal_checkout_ic_card_empty_state">es.xml</item>
    <item type="drawable" name="paypal_checkout_ic_cb">agc.xml</item>
    <item type="drawable" name="paypal_checkout_ic_chase">jy.xml</item>
    <item type="drawable" name="paypal_checkout_ic_check">e.xml</item>
    <item type="drawable" name="paypal_checkout_ic_checkmark_circle">agf.xml</item>
    <item type="drawable" name="paypal_checkout_ic_checkmark_circle_blue">rq.xml</item>
    <item type="drawable" name="paypal_checkout_ic_close">sk.xml</item>
    <item type="drawable" name="paypal_checkout_ic_cofinoga">abe.xml</item>
    <item type="drawable" name="paypal_checkout_ic_crypto">br.xml</item>
    <item type="drawable" name="paypal_checkout_ic_crypto_bg">jw.xml</item>
    <item type="drawable" name="paypal_checkout_ic_currency_conversion_icon">afu.xml</item>
    <item type="drawable" name="paypal_checkout_ic_diners_mini">fy.xml</item>
    <item type="drawable" name="paypal_checkout_ic_discover">ql.xml</item>
    <item type="drawable" name="paypal_checkout_ic_discover_mini">ba.xml</item>
    <item type="drawable" name="paypal_checkout_ic_done_check">co.xml</item>
    <item type="drawable" name="paypal_checkout_ic_downarrowproductname">agj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_ebay_mastercard">su.xml</item>
    <item type="drawable" name="paypal_checkout_ic_elo">kh.xml</item>
    <item type="drawable" name="paypal_checkout_ic_error">aif.xml</item>
    <item type="drawable" name="paypal_checkout_ic_error_cricle">lz.xml</item>
    <item type="drawable" name="paypal_checkout_ic_expand_close">afv.xml</item>
    <item type="drawable" name="paypal_checkout_ic_expand_open">d.xml</item>
    <item type="drawable" name="paypal_checkout_ic_hiper">ik.xml</item>
    <item type="drawable" name="paypal_checkout_ic_hipercard">en.xml</item>
    <item type="drawable" name="paypal_checkout_ic_lock_72_hour">he.xml</item>
    <item type="drawable" name="paypal_checkout_ic_logo">aw.xml</item>
    <item type="drawable" name="paypal_checkout_ic_maestro">agq.xml</item>
    <item type="drawable" name="paypal_checkout_ic_master_mini">ahj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_mc">bj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_newestpplogoforspinner">akb.xml</item>
    <item type="drawable" name="paypal_checkout_ic_newtruck">px.xml</item>
    <item type="drawable" name="paypal_checkout_ic_path">ait.xml</item>
    <item type="drawable" name="paypal_checkout_ic_pay_later_bg_green">fz.xml</item>
    <item type="drawable" name="paypal_checkout_ic_payment_card_gray_bgd">u.xml</item>
    <item type="drawable" name="paypal_checkout_ic_payment_card_gray_bgd_vector">ar.xml</item>
    <item type="drawable" name="paypal_checkout_ic_paypal_cashback_mastercard">adt.xml</item>
    <item type="drawable" name="paypal_checkout_ic_pick_up">bp.xml</item>
    <item type="drawable" name="paypal_checkout_ic_pp_credit">sc.xml</item>
    <item type="drawable" name="paypal_checkout_ic_pp_smart_connect">aja.xml</item>
    <item type="drawable" name="paypal_checkout_ic_ppc_cardart">ago.xml</item>
    <item type="drawable" name="paypal_checkout_ic_pplogo3">anm.xml</item>
    <item type="drawable" name="paypal_checkout_ic_ppmc">cm.xml</item>
    <item type="drawable" name="paypal_checkout_ic_preferred_bg_orange_border">ct.xml</item>
    <item type="drawable" name="paypal_checkout_ic_preferred_bg_white">akj.xml</item>
    <item type="drawable" name="paypal_checkout_ic_preferred_gray">alv.xml</item>
    <item type="drawable" name="paypal_checkout_ic_preferred_orange">cv.xml</item>
    <item type="drawable" name="paypal_checkout_ic_selling_crypto">ana.xml</item>
    <item type="drawable" name="paypal_checkout_ic_shipping_option">abh.xml</item>
    <item type="drawable" name="paypal_checkout_ic_store_exit_animation">qq.xml</item>
    <item type="drawable" name="paypal_checkout_ic_store_exit_crypto">nh.xml</item>
    <item type="drawable" name="paypal_checkout_ic_store_exit_transition">hy.xml</item>
    <item type="drawable" name="paypal_checkout_ic_union_pay">ajz.xml</item>
    <item type="drawable" name="paypal_checkout_ic_updatedvisa">bv.xml</item>
    <item type="drawable" name="paypal_checkout_ic_visa_mini">abf.xml</item>
    <item type="drawable" name="paypal_checkout_ic_warning">sg.xml</item>
    <item type="drawable" name="paypal_checkout_ic_warning_grey_24dp">pp.xml</item>
    <item type="drawable" name="paypal_checkout_large_progress">akg.xml</item>
    <item type="drawable" name="paypal_checkout_loading_gifts">eo</item>
    <item type="drawable" name="paypal_checkout_logo_paypal_color">ajh.xml</item>
    <item type="drawable" name="paypal_checkout_logo_paypal_monochrome">kj.xml</item>
    <item type="drawable" name="paypal_checkout_native_add_card_rectangle">akc.xml</item>
    <item type="drawable" name="paypal_checkout_pick_up_options_ic_info">gl.xml</item>
    <item type="drawable" name="paypal_checkout_plus">x.xml</item>
    <item type="drawable" name="paypal_checkout_pypl_cards_banner_background">qh.xml</item>
    <item type="drawable" name="paypal_checkout_round_button_background_for_profile">kv.xml</item>
    <item type="drawable" name="paypal_checkout_rounded_dialog">amz.xml</item>
    <item type="drawable" name="paypal_checkout_rounded_dialog_loading">sw.xml</item>
    <item type="drawable" name="paypal_checkout_rounded_view">bx.xml</item>
    <item type="drawable" name="paypal_checkout_three_ds_anim">sj.xml</item>
    <item type="drawable" name="paypal_checkout_warning_shape_bg">iz.xml</item>
    <item type="drawable" name="paypal_checkout_wider9patch">xy</item>
    <item type="drawable" name="paypal_checkout_wordmark_paypal_color">fu.xml</item>
    <item type="drawable" name="paypal_checkout_wordmark_paypal_credit_monochrome">kt.xml</item>
    <item type="drawable" name="paypal_checkout_wordmark_paypal_monochrome">lk.xml</item>
    <item type="drawable" name="paypal_edittext">od.xml</item>
    <item type="drawable" name="plus">ko.xml</item>
    <item type="drawable" name="preference_list_divider_material">ng.xml</item>
    <item type="drawable" name="premium_bg">pn.xml</item>
    <item type="drawable" name="premium_bg_alpha_50">hh.xml</item>
    <item type="drawable" name="privileges_bg">afe.xml</item>
    <item type="drawable" name="progress_spinner">aix.xml</item>
    <item type="drawable" name="rectangle_green_stroke_radius8">aip.xml</item>
    <item type="drawable" name="rectangle_grey_radius8">ry.xml</item>
    <item type="drawable" name="replay">afr.xml</item>
    <item type="drawable" name="rounded_bottom_sheet_loading">amx.xml</item>
    <item type="drawable" name="scroll_bar">hw.xml</item>
    <item type="drawable" name="scroll_bar_bg">gm.xml</item>
    <item type="drawable" name="seekbar_progress_drawable">aef.xml</item>
    <item type="drawable" name="selector_button_kit">aem.xml</item>
    <item type="drawable" name="selector_checkbox">agr.xml</item>
    <item type="drawable" name="selector_checkbox_small">ajw.xml</item>
    <item type="drawable" name="selector_edittext">ci.xml</item>
    <item type="drawable" name="selector_imageview">mh.xml</item>
    <item type="drawable" name="selector_switch">rt.xml</item>
    <item type="drawable" name="shape_progress_foreground">b.xml</item>
    <item type="drawable" name="share">au.xml</item>
    <item type="drawable" name="sic_add">ajg.xml</item>
    <item type="drawable" name="sic_delete">pi.xml</item>
    <item type="drawable" name="splash_window">te.xml</item>
    <item type="drawable" name="start_pay_btn">eq.xml</item>
    <item type="drawable" name="tab_home">anr.xml</item>
    <item type="drawable" name="tab_home_selected">ano.xml</item>
    <item type="drawable" name="tab_home_unselected">aje.xml</item>
    <item type="drawable" name="tab_recording">rz.xml</item>
    <item type="drawable" name="tab_recording_selected">gx.xml</item>
    <item type="drawable" name="tab_recording_unselected">hg.xml</item>
    <item type="drawable" name="test_custom_background">r.xml</item>
    <item type="drawable" name="test_level_drawable">qu.xml</item>
    <item type="drawable" name="tooltip_frame_dark">abp.xml</item>
    <item type="drawable" name="tooltip_frame_light">mz.xml</item>
    <item type="drawable" name="vip_good_list_item_selected">gb.xml</item>
    <item type="drawable" name="vip_good_list_item_selector">ael.xml</item>
    <item type="drawable" name="vip_good_list_item_un_selected">mm.xml</item>
    <item type="drawable" name="warning">asf</item>
    <item type="drawable" name="edit_text_background">aum.xml</item>
    <item type="drawable" name="button_background">aun.xml</item>
</resources>
