.class public final Lcom/abox/apps/activitys/OrderHistoryActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/activitys/OrderHistoryActivity$Activity;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0018\u0000 \r2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\rB\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0008\u001a\u00020\u00022\u0006\u0010\t\u001a\u00020\nH\u0014J\u0008\u0010\u000b\u001a\u00020\u000cH\u0014R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"
    }
    d2 = {
        "Lcom/abox/apps/activitys/OrderHistoryActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;",
        "()V",
        "historyListAdapter",
        "Lcom/abox/apps/adapters/HistoryListAdapter;",
        "recyclerViewSkeletonScreen",
        "Lcom/ethanhua/skeleton/RecyclerViewSkeletonScreen;",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nOrderHistoryActivity.kt\nKotlin\n*S Kotlin\n*F\n+ 1 OrderHistoryActivity.kt\ncom/abox/apps/activitys/OrderHistoryActivity\n+ 2 CoroutineExceptionHandler.kt\nkotlinx/coroutines/CoroutineExceptionHandlerKt\n*L\n1#1,107:1\n48#2,4:108\n*S KotlinDebug\n*F\n+ 1 OrderHistoryActivity.kt\ncom/abox/apps/activitys/OrderHistoryActivity\n*L\n85#1:108,4\n*E\n"
    }
.end annotation


# static fields
.field public static final asInterface:Ljava/lang/String; = "DrawerMenu"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final getDefaultImpl:Ljava/lang/String; = "VIPPage"
    .annotation build Lo/cbz;
    .end annotation
.end field

.field public static final setDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity$Activity;
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field private IconCompatParcelizer:Lo/StorageEventListener;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private asBinder:Lcom/abox/apps/adapters/HistoryListAdapter;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/activitys/OrderHistoryActivity$Activity;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/activitys/OrderHistoryActivity$Activity;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/activitys/OrderHistoryActivity;->setDefaultImpl:Lcom/abox/apps/activitys/OrderHistoryActivity$Activity;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    return-void
.end method

.method public static final synthetic asBinder(Lcom/abox/apps/activitys/OrderHistoryActivity;Lo/StorageEventListener;)V
    .locals 0

    iput-object p1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->IconCompatParcelizer:Lo/StorageEventListener;

    return-void
.end method

.method public static final synthetic asInterface(Lcom/abox/apps/activitys/OrderHistoryActivity;)Lo/StorageEventListener;
    .locals 0

    iget-object p0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->IconCompatParcelizer:Lo/StorageEventListener;

    return-object p0
.end method

.method public static synthetic asInterface(Lcom/abox/apps/activitys/OrderHistoryActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/OrderHistoryActivity;->getDefaultImpl(Lcom/abox/apps/activitys/OrderHistoryActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final getDefaultImpl(Lcom/abox/apps/activitys/OrderHistoryActivity;Landroid/view/View;)V
    .locals 0

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public static final synthetic setDefaultImpl(Lcom/abox/apps/activitys/OrderHistoryActivity;)Lcom/abox/apps/adapters/HistoryListAdapter;
    .locals 0

    iget-object p0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->asBinder:Lcom/abox/apps/adapters/HistoryListAdapter;

    return-object p0
.end method


# virtual methods
.method protected getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;
    .locals 1
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->setDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method protected onTransact()V
    .locals 10

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v0

    const-string v1, "entryType"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    sget-object v1, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string v2, "enterODH"

    invoke-virtual {v1, v2}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    const-string v2, "DrawerMenu"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    const-string v2, "enterODHDrawerMenu"

    invoke-virtual {v1, v2}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const-string v2, "VIPPage"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    const-string v2, "enterODHVipPage"

    invoke-virtual {v1, v2}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    :cond_1
    :goto_0
    sget-object v1, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    new-instance v2, Landroid/os/Bundle;

    invoke-direct {v2}, Landroid/os/Bundle;-><init>()V

    const-string v3, "entry"

    invoke-virtual {v2, v3, v0}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    sget-object v0, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    const-string v0, "enterOrderHistory"

    invoke-virtual {v1, v0, v2}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;Landroid/os/Bundle;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->asBinder:Landroid/widget/ImageView;

    new-instance v1, Lo/MovementMethod;

    invoke-direct {v1, p0}, Lo/MovementMethod;-><init>(Lcom/abox/apps/activitys/OrderHistoryActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    sget v0, Lo/Cursor$ActionBar;->addOnMultiWindowModeChangedListener:I

    invoke-static {v0}, Lo/aln;->asInterface(I)I

    move-result v0

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->onTransact:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v2, Lcom/abox/apps/views/VerticalSpaceItemDecoration;

    invoke-direct {v2, v0}, Lcom/abox/apps/views/VerticalSpaceItemDecoration;-><init>(I)V

    invoke-virtual {v1, v2}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$ItemDecoration;)V

    new-instance v0, Lcom/abox/apps/adapters/HistoryListAdapter;

    sget v1, Lo/Cursor$Fragment;->Api26Impl$$ExternalSyntheticApiModelOutline1:I

    invoke-direct {v0, v1}, Lcom/abox/apps/adapters/HistoryListAdapter;-><init>(I)V

    iput-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->asBinder:Lcom/abox/apps/adapters/HistoryListAdapter;

    invoke-static {p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0}, Lcom/abox/apps/databinding/EmptyLayoutBinding;->asInterface(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/EmptyLayoutBinding;

    move-result-object v0

    const-string v1, "inflate(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v1, v0, Lcom/abox/apps/databinding/EmptyLayoutBinding;->onTransact:Landroid/widget/ImageView;

    sget v2, Lo/Cursor$FragmentManager;->Api26Impl:I

    invoke-virtual {v1, v2}, Landroid/widget/ImageView;->setImageResource(I)V

    iget-object v1, v0, Lcom/abox/apps/databinding/EmptyLayoutBinding;->getDefaultImpl:Landroid/widget/TextView;

    sget v2, Lo/Cursor$TaskStackBuilder;->PipHintTrackerKt$trackPipAnimationHintView$2:I

    invoke-static {v2}, Lo/aln;->asBinder(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->asBinder:Lcom/abox/apps/adapters/HistoryListAdapter;

    const-string v2, "historyListAdapter"

    const/4 v3, 0x0

    if-nez v1, :cond_2

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v1, v3

    :cond_2
    invoke-virtual {v0}, Lcom/abox/apps/databinding/EmptyLayoutBinding;->getDefaultImpl()Landroidx/constraintlayout/widget/ConstraintLayout;

    move-result-object v0

    const-string v4, "getRoot(...)"

    invoke-static {v0, v4}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {v1, v0}, Lcom/chad/library/adapter/base/BaseQuickAdapter;->read(Landroid/view/View;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->onTransact:Landroidx/recyclerview/widget/RecyclerView;

    iget-object v1, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->asBinder:Lcom/abox/apps/adapters/HistoryListAdapter;

    if-nez v1, :cond_3

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->throwUninitializedPropertyAccessException(Ljava/lang/String;)V

    move-object v1, v3

    :cond_3
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    iget-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->IconCompatParcelizer:Lo/StorageEventListener;

    if-eqz v0, :cond_4

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lo/StorageEventListener;->setDefaultImpl()V

    :cond_4
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->onTransact:Landroidx/recyclerview/widget/RecyclerView;

    invoke-static {v0}, Lo/OnObbStateChangeListener;->getDefaultImpl(Landroidx/recyclerview/widget/RecyclerView;)Lo/StorageEventListener$Activity;

    move-result-object v0

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;->onTransact:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    move-result-object v1

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->asInterface(Landroidx/recyclerview/widget/RecyclerView$Adapter;)Lo/StorageEventListener$Activity;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->setDefaultImpl(Z)Lo/StorageEventListener$Activity;

    move-result-object v0

    const/16 v1, 0x14

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->setDefaultImpl(I)Lo/StorageEventListener$Activity;

    move-result-object v0

    const/16 v1, 0x4b0

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->getDefaultImpl(I)Lo/StorageEventListener$Activity;

    move-result-object v0

    const/4 v1, 0x5

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->asBinder(I)Lo/StorageEventListener$Activity;

    move-result-object v0

    sget v1, Lo/Cursor$TaskDescription;->asBinder:I

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->asInterface(I)Lo/StorageEventListener$Activity;

    move-result-object v0

    sget v1, Lo/Cursor$Fragment;->ActivityViewModelLazyKt$viewModels$2:I

    invoke-virtual {v0, v1}, Lo/StorageEventListener$Activity;->onTransact(I)Lo/StorageEventListener$Activity;

    move-result-object v0

    invoke-virtual {v0}, Lo/StorageEventListener$Activity;->getDefaultImpl()Lo/StorageEventListener;

    move-result-object v0

    iput-object v0, p0, Lcom/abox/apps/activitys/OrderHistoryActivity;->IconCompatParcelizer:Lo/StorageEventListener;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lo/StorageEventListener;->onTransact()V

    :cond_5
    invoke-static {p0}, Landroidx/lifecycle/LifecycleOwnerKt;->getLifecycleScope(Landroidx/lifecycle/LifecycleOwner;)Landroidx/lifecycle/LifecycleCoroutineScope;

    move-result-object v4

    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getIO()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v0

    new-instance v1, Lcom/abox/apps/activitys/OrderHistoryActivity$TaskDescription;

    sget-object v2, Lkotlinx/coroutines/CoroutineExceptionHandler;->Key:Lkotlinx/coroutines/CoroutineExceptionHandler$Key;

    invoke-direct {v1, v2, p0}, Lcom/abox/apps/activitys/OrderHistoryActivity$TaskDescription;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$Key;Lcom/abox/apps/activitys/OrderHistoryActivity;)V

    invoke-virtual {v0, v1}, Lkotlin/coroutines/AbstractCoroutineContextElement;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    move-result-object v5

    const/4 v6, 0x0

    new-instance v7, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;

    invoke-direct {v7, p0, v3}, Lcom/abox/apps/activitys/OrderHistoryActivity$Application;-><init>(Lcom/abox/apps/activitys/OrderHistoryActivity;Lkotlin/coroutines/Continuation;)V

    const/4 v8, 0x2

    const/4 v9, 0x0

    invoke-static/range {v4 .. v9}, Lkotlinx/coroutines/BuildersKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/OrderHistoryActivity;->getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityOrderHistoryBinding;

    move-result-object p1

    return-object p1
.end method
