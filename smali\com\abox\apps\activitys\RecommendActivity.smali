.class public final Lcom/abox/apps/activitys/RecommendActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/activitys/RecommendActivity$TaskDescription;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivityRecommendBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0018\u0000 \n2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\nB\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0006H\u0014J\u0008\u0010\u0007\u001a\u00020\u0008H\u0014J\u0008\u0010\t\u001a\u00020\u0008H\u0014\u00a8\u0006\u000b"
    }
    d2 = {
        "Lcom/abox/apps/activitys/RecommendActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivityRecommendBinding;",
        "()V",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "",
        "onPause",
        "Companion",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final asBinder:Lcom/abox/apps/activitys/RecommendActivity$TaskDescription;
    .annotation build Lo/cbz;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/activitys/RecommendActivity$TaskDescription;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/activitys/RecommendActivity$TaskDescription;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/activitys/RecommendActivity;->asBinder:Lcom/abox/apps/activitys/RecommendActivity$TaskDescription;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    return-void
.end method

.method private static final asBinder(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V
    .locals 7

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Lcom/abox/apps/activitys/FreeTryActivity;->getDefaultImpl:Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;

    const/4 v2, 0x4

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xc

    const/4 v6, 0x0

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;->asInterface(Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;Landroid/content/Context;ILjava/lang/String;IILjava/lang/Object;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public static synthetic asInterface(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecommendActivity;->onTransact(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final asInterface(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 7

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "OnTouchListener event:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    invoke-static {p1, v0}, Lo/eq;->onTransact(Ljava/lang/String;[Ljava/lang/Object;)V

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result p1

    if-nez p1, :cond_0

    sget-object v0, Lcom/abox/apps/activitys/FreeTryActivity;->getDefaultImpl:Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;

    const/4 v2, 0x4

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xc

    const/4 v6, 0x0

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;->asInterface(Lcom/abox/apps/activitys/FreeTryActivity$ActionBar;Landroid/content/Context;ILjava/lang/String;IILjava/lang/Object;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    const/4 p0, 0x1

    return p0

    :cond_0
    invoke-super {p0, p2}, Landroid/app/Activity;->onTouchEvent(Landroid/view/MotionEvent;)Z

    move-result p0

    return p0
.end method

.method public static synthetic getDefaultImpl(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RecommendActivity;->asBinder(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic getDefaultImpl(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/abox/apps/activitys/RecommendActivity;->asInterface(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;Landroid/view/MotionEvent;)Z

    move-result p0

    return p0
.end method

.method private static final onTransact(Lcom/abox/apps/activitys/RecommendActivity;Landroid/view/View;)V
    .locals 1

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string/jumbo v0, "vphClose"

    invoke-virtual {p1, v0}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object p1, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    const-string v0, "closeVipPopupHome"

    invoke-virtual {p1, v0}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method


# virtual methods
.method protected getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendBinding;
    .locals 1
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lcom/abox/apps/databinding/ActivityRecommendBinding;->asInterface(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method public onPause()V
    .locals 1

    invoke-super {p0}, Landroidx/fragment/app/FragmentActivity;->onPause()V

    const/4 v0, 0x0

    invoke-virtual {p0, v0, v0}, Landroid/app/Activity;->overridePendingTransition(II)V

    return-void
.end method

.method protected onTransact()V
    .locals 3

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string/jumbo v1, "vipPopupHome"

    invoke-virtual {v0, v1}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    const-string/jumbo v1, "showVipPopupHome"

    invoke-virtual {v0, v1}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-static {p0, v0}, Lo/CloneNotSupportedException;->onTransact(Landroid/app/Activity;I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;->onTransact:Landroid/widget/ImageView;

    new-instance v2, Lo/KeyListener;

    invoke-direct {v2, p0}, Lo/KeyListener;-><init>(Lcom/abox/apps/activitys/RecommendActivity;)V

    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;->setDefaultImpl:Landroid/widget/TextView;

    invoke-virtual {v1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    move-result-object v1

    const/16 v2, 0x10

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setFlags(I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;->setDefaultImpl:Landroid/widget/TextView;

    invoke-virtual {v1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    sget-object v0, Lo/LineNumberReader;->setDefaultImpl:Lo/LineNumberReader;

    invoke-virtual {v0}, Lo/LineNumberReader;->onTransact()Lcom/abox/apps/model/CommonConfig;

    move-result-object v1

    invoke-virtual {v1}, Lcom/abox/apps/model/CommonConfig;->getOriginalPrice()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v2

    check-cast v2, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v2, v2, Lcom/abox/apps/databinding/ActivityRecommendBinding;->setDefaultImpl:Landroid/widget/TextView;

    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_0
    invoke-virtual {v0}, Lo/LineNumberReader;->onTransact()Lcom/abox/apps/model/CommonConfig;

    move-result-object v0

    invoke-virtual {v0}, Lcom/abox/apps/model/CommonConfig;->getCurrentPrice()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityRecommendBinding;->asBinder:Landroid/widget/TextView;

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_1
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityRecommendBinding;->RemoteActionCompatParcelizer:Landroid/widget/TextView;

    new-instance v1, Lo/TransitionManager;

    invoke-direct {v1, p0}, Lo/TransitionManager;-><init>(Lcom/abox/apps/activitys/RecommendActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityRecommendBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityRecommendBinding;->IconCompatParcelizer:Landroidx/constraintlayout/widget/ConstraintLayout;

    new-instance v1, Lo/UpdateAppearance;

    invoke-direct {v1, p0}, Lo/UpdateAppearance;-><init>(Lcom/abox/apps/activitys/RecommendActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    sget-object v0, Lo/PrintWriter;->setDefaultImpl:Lo/PrintWriter;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lo/PrintWriter;->asInterface(J)V

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/RecommendActivity;->getDefaultImpl(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityRecommendBinding;

    move-result-object p1

    return-object p1
.end method
