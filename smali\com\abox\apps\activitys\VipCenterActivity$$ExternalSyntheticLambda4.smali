.class public final synthetic Lcom/abox/apps/activitys/VipCenterActivity$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/activity/result/ActivityResultCallback;


# instance fields
.field public final synthetic getDefaultImpl:Lcom/abox/apps/activitys/VipCenterActivity;


# direct methods
.method public synthetic constructor <init>(Lcom/abox/apps/activitys/VipCenterActivity;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/abox/apps/activitys/VipCenterActivity$$ExternalSyntheticLambda4;->getDefaultImpl:Lcom/abox/apps/activitys/VipCenterActivity;

    return-void
.end method


# virtual methods
.method public final onActivityResult(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/abox/apps/activitys/VipCenterActivity$$ExternalSyntheticLambda4;->getDefaultImpl:Lcom/abox/apps/activitys/VipCenterActivity;

    check-cast p1, Landroidx/activity/result/ActivityResult;

    invoke-static {v0, p1}, Lcom/abox/apps/activitys/VipCenterActivity;->asInterface(Lcom/abox/apps/activitys/VipCenterActivity;Landroidx/activity/result/ActivityResult;)V

    return-void
.end method
