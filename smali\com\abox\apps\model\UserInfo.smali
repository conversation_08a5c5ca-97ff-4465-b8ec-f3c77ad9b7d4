.class public final Lcom/abox/apps/model/UserInfo;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/abox/apps/model/UserInfo$CREATOR;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u000c\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008[\n\u0002\u0010\u0000\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008\u0086\u0008\u0018\u0000 z2\u00020\u0001:\u0001zB\u000f\u0008\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004B\u00b3\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\n\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\r\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u000e\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u000f\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0008\u0012\u0006\u0010\u0013\u001a\u00020\u0006\u0012\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015\u0012\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u0018\u0012\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0008\u0002\u0010\u001c\u001a\u00020\u0015\u0012\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u0008\u0012\u0006\u0010 \u001a\u00020\u0018\u0012\u0006\u0010!\u001a\u00020\u0006\u0012\u0008\u0010\"\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010#\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010$\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010%\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010&\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\'\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0002\u0010(J\t\u0010P\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010R\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010S\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\t\u0010T\u001a\u00020\u0006H\u00c6\u0003J\u0010\u0010U\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00109J\u0010\u0010V\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00109J\t\u0010W\u001a\u00020\u0018H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010Y\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0015H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010^\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\t\u0010`\u001a\u00020\u0018H\u00c6\u0003J\t\u0010a\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010b\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010c\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010d\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010e\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010f\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010g\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010h\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010i\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010j\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010k\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010l\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010m\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u000b\u0010n\u001a\u0004\u0018\u00010\u0008H\u00c6\u0003J\u00f6\u0002\u0010o\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00062\n\u0008\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\t\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\n\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u000c\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\r\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00082\u0008\u0008\u0002\u0010\u0013\u001a\u00020\u00062\n\u0008\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\u0008\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00152\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u00182\n\u0008\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00082\u0008\u0008\u0002\u0010\u001c\u001a\u00020\u00152\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u00082\u0008\u0008\u0002\u0010 \u001a\u00020\u00182\u0008\u0008\u0002\u0010!\u001a\u00020\u00062\n\u0008\u0002\u0010\"\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010#\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010$\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010%\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010&\u001a\u0004\u0018\u00010\u00082\n\u0008\u0002\u0010\'\u001a\u0004\u0018\u00010\u0008H\u00c6\u0001\u00a2\u0006\u0002\u0010pJ\u0008\u0010q\u001a\u00020\u0006H\u0016J\u0013\u0010r\u001a\u00020\u00152\u0008\u0010s\u001a\u0004\u0018\u00010tH\u00d6\u0003J\t\u0010u\u001a\u00020\u0006H\u00d6\u0001J\t\u0010v\u001a\u00020\u0008H\u00d6\u0001J\u0018\u0010w\u001a\u00020x2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010y\u001a\u00020\u0006H\u0016R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008)\u0010*R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008+\u0010,R\u0013\u0010\"\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008-\u0010,R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008.\u0010,R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008/\u0010,R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00080\u0010,R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00081\u0010,R\u0013\u0010\'\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00082\u0010,R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00083\u0010,R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00084\u00105R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00086\u0010,R\u0013\u0010#\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00087\u0010,R\u0011\u0010\u001c\u001a\u00020\u0015\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u001c\u00108R\u0015\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u0010:\u001a\u0004\u0008\u0014\u00109R\u001e\u0010\u0016\u001a\u0004\u0018\u00010\u0015X\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010:\u001a\u0004\u0008\u0016\u00109\"\u0004\u0008;\u0010<R\u0013\u0010\u000c\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008=\u0010,R\u0011\u0010 \u001a\u00020\u0018\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008>\u00105R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008?\u0010,R\u0013\u0010&\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008@\u0010,R\u0013\u0010\u001d\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008A\u0010,R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008B\u0010,R\u0013\u0010$\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008C\u0010,R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008D\u0010,R\u0013\u0010%\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008E\u0010,R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008F\u0010,R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008G\u0010,R\u0011\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008H\u0010*R\u001c\u0010\u001b\u001a\u0004\u0018\u00010\u0008X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008I\u0010,\"\u0004\u0008J\u0010KR\u0011\u0010!\u001a\u00020\u0006\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008L\u0010*R\u001c\u0010\u001a\u001a\u0004\u0018\u00010\u0008X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008M\u0010,\"\u0004\u0008N\u0010KR\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008O\u0010,\u00a8\u0006{"
    }
    d2 = {
        "Lcom/abox/apps/model/UserInfo;",
        "Landroid/os/Parcelable;",
        "parcel",
        "Landroid/os/Parcel;",
        "(Landroid/os/Parcel;)V",
        "appId",
        "",
        "deviceKey",
        "",
        "name",
        "channel",
        "versionCode",
        "language",
        "createBy",
        "createTime",
        "updateBy",
        "updateTime",
        "remark",
        "status",
        "userId",
        "isNew",
        "",
        "isVip",
        "expireDate",
        "",
        "email",
        "userType",
        "userIdText",
        "isForever",
        "region",
        "deviceInfo",
        "hwAccount",
        "loginTime",
        "userState",
        "costMoney",
        "hwUnionId",
        "startDate",
        "token",
        "operation",
        "durationTime",
        "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "getAppId",
        "()I",
        "getChannel",
        "()Ljava/lang/String;",
        "getCostMoney",
        "getCreateBy",
        "getCreateTime",
        "getDeviceInfo",
        "getDeviceKey",
        "getDurationTime",
        "getEmail",
        "getExpireDate",
        "()J",
        "getHwAccount",
        "getHwUnionId",
        "()Z",
        "()Ljava/lang/Boolean;",
        "Ljava/lang/Boolean;",
        "setVip",
        "(Ljava/lang/Boolean;)V",
        "getLanguage",
        "getLoginTime",
        "getName",
        "getOperation",
        "getRegion",
        "getRemark",
        "getStartDate",
        "getStatus",
        "getToken",
        "getUpdateBy",
        "getUpdateTime",
        "getUserId",
        "getUserIdText",
        "setUserIdText",
        "(Ljava/lang/String;)V",
        "getUserState",
        "getUserType",
        "setUserType",
        "getVersionCode",
        "component1",
        "component10",
        "component11",
        "component12",
        "component13",
        "component14",
        "component15",
        "component16",
        "component17",
        "component18",
        "component19",
        "component2",
        "component20",
        "component21",
        "component22",
        "component23",
        "component24",
        "component25",
        "component26",
        "component27",
        "component28",
        "component29",
        "component3",
        "component30",
        "component31",
        "component4",
        "component5",
        "component6",
        "component7",
        "component8",
        "component9",
        "copy",
        "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/abox/apps/model/UserInfo;",
        "describeContents",
        "equals",
        "other",
        "",
        "hashCode",
        "toString",
        "writeToParcel",
        "",
        "flags",
        "CREATOR",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final CREATOR:Lcom/abox/apps/model/UserInfo$CREATOR;
    .annotation build Lo/cbz;
    .end annotation
.end field


# instance fields
.field private final appId:I

.field private final channel:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final costMoney:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final createBy:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final createTime:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final deviceInfo:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final deviceKey:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final durationTime:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final email:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final expireDate:J

.field private final hwAccount:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final hwUnionId:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final isForever:Z

.field private final isNew:Ljava/lang/Boolean;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private isVip:Ljava/lang/Boolean;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final language:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final loginTime:J

.field private final name:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final operation:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final region:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final remark:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final startDate:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final status:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final token:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final updateBy:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final updateTime:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final userId:I

.field private userIdText:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final userState:I

.field private userType:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private final versionCode:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/abox/apps/model/UserInfo$CREATOR;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/abox/apps/model/UserInfo$CREATOR;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/abox/apps/model/UserInfo;->CREATOR:Lcom/abox/apps/model/UserInfo$CREATOR;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 3
    .param p2    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p9    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p10    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p12    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p14    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p15    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p18    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p19    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p20    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p22    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p23    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p24    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p28    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p29    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p30    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p31    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p32    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p33    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    move-object v0, p0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move v1, p1

    iput v1, v0, Lcom/abox/apps/model/UserInfo;->appId:I

    move-object v1, p2

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    move-object v1, p3

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    move-object v1, p4

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    move-object v1, p5

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    move-object v1, p6

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    move-object v1, p7

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    move-object v1, p8

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    move-object v1, p9

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    move-object v1, p10

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    move-object v1, p11

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    move-object v1, p12

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    move/from16 v1, p13

    iput v1, v0, Lcom/abox/apps/model/UserInfo;->userId:I

    move-object/from16 v1, p14

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    move-object/from16 v1, p15

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    move-wide/from16 v1, p16

    iput-wide v1, v0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    move-object/from16 v1, p18

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    move-object/from16 v1, p19

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    move-object/from16 v1, p20

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    move/from16 v1, p21

    iput-boolean v1, v0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    move-object/from16 v1, p22

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    move-object/from16 v1, p23

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    move-object/from16 v1, p24

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    move-wide/from16 v1, p25

    iput-wide v1, v0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    move/from16 v1, p27

    iput v1, v0, Lcom/abox/apps/model/UserInfo;->userState:I

    move-object/from16 v1, p28

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    move-object/from16 v1, p29

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    move-object/from16 v1, p30

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    move-object/from16 v1, p31

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    move-object/from16 v1, p32

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    move-object/from16 v1, p33

    iput-object v1, v0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 36

    const v0, 0x8000

    and-int v0, p34, v0

    if-eqz v0, :cond_0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    move-wide/from16 v18, v0

    goto :goto_0

    :cond_0
    move-wide/from16 v18, p16

    :goto_0
    const/high16 v0, 0x80000

    and-int v0, p34, v0

    if-eqz v0, :cond_1

    const/4 v0, 0x0

    move/from16 v23, v0

    goto :goto_1

    :cond_1
    move/from16 v23, p21

    :goto_1
    move-object/from16 v2, p0

    move/from16 v3, p1

    move-object/from16 v4, p2

    move-object/from16 v5, p3

    move-object/from16 v6, p4

    move-object/from16 v7, p5

    move-object/from16 v8, p6

    move-object/from16 v9, p7

    move-object/from16 v10, p8

    move-object/from16 v11, p9

    move-object/from16 v12, p10

    move-object/from16 v13, p11

    move-object/from16 v14, p12

    move/from16 v15, p13

    move-object/from16 v16, p14

    move-object/from16 v17, p15

    move-object/from16 v20, p18

    move-object/from16 v21, p19

    move-object/from16 v22, p20

    move-object/from16 v24, p22

    move-object/from16 v25, p23

    move-object/from16 v26, p24

    move-wide/from16 v27, p25

    move/from16 v29, p27

    move-object/from16 v30, p28

    move-object/from16 v31, p29

    move-object/from16 v32, p30

    move-object/from16 v33, p31

    move-object/from16 v34, p32

    move-object/from16 v35, p33

    invoke-direct/range {v2 .. v35}, Lcom/abox/apps/model/UserInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Landroid/os/Parcel;)V
    .locals 36
    .param p1    # Landroid/os/Parcel;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    move-object/from16 v0, p1

    const-string v1, "parcel"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readInt()I

    move-result v3

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v12

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v14

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readInt()I

    move-result v15

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/os/Parcel;->readValue(Ljava/lang/ClassLoader;)Ljava/lang/Object;

    move-result-object v2

    move/from16 v16, v15

    instance-of v15, v2, Ljava/lang/Boolean;

    const/16 v17, 0x0

    if-eqz v15, :cond_0

    check-cast v2, Ljava/lang/Boolean;

    move-object/from16 v18, v2

    goto :goto_0

    :cond_0
    move-object/from16 v18, v17

    :goto_0
    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/os/Parcel;->readValue(Ljava/lang/ClassLoader;)Ljava/lang/Object;

    move-result-object v1

    instance-of v2, v1, Ljava/lang/Boolean;

    if-eqz v2, :cond_1

    check-cast v1, Ljava/lang/Boolean;

    move-object/from16 v17, v1

    :cond_1
    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v19

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readByte()B

    move-result v2

    if-eqz v2, :cond_2

    const/4 v2, 0x1

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    :goto_1
    move/from16 v23, v2

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v24

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v25

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v26

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v27

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readInt()I

    move-result v29

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v30

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v31

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v32

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v33

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v34

    invoke-virtual/range {p1 .. p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v35

    move-object/from16 v2, p0

    move/from16 v15, v16

    move-object/from16 v16, v18

    move-wide/from16 v18, v19

    move-object/from16 v20, v1

    invoke-direct/range {v2 .. v35}, Lcom/abox/apps/model/UserInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic copy$default(Lcom/abox/apps/model/UserInfo;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lcom/abox/apps/model/UserInfo;
    .locals 17

    move-object/from16 v0, p0

    move/from16 v1, p34

    and-int/lit8 v2, v1, 0x1

    if-eqz v2, :cond_0

    iget v2, v0, Lcom/abox/apps/model/UserInfo;->appId:I

    goto :goto_0

    :cond_0
    move/from16 v2, p1

    :goto_0
    and-int/lit8 v3, v1, 0x2

    if-eqz v3, :cond_1

    iget-object v3, v0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    goto :goto_1

    :cond_1
    move-object/from16 v3, p2

    :goto_1
    and-int/lit8 v4, v1, 0x4

    if-eqz v4, :cond_2

    iget-object v4, v0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    goto :goto_2

    :cond_2
    move-object/from16 v4, p3

    :goto_2
    and-int/lit8 v5, v1, 0x8

    if-eqz v5, :cond_3

    iget-object v5, v0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    goto :goto_3

    :cond_3
    move-object/from16 v5, p4

    :goto_3
    and-int/lit8 v6, v1, 0x10

    if-eqz v6, :cond_4

    iget-object v6, v0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    goto :goto_4

    :cond_4
    move-object/from16 v6, p5

    :goto_4
    and-int/lit8 v7, v1, 0x20

    if-eqz v7, :cond_5

    iget-object v7, v0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    goto :goto_5

    :cond_5
    move-object/from16 v7, p6

    :goto_5
    and-int/lit8 v8, v1, 0x40

    if-eqz v8, :cond_6

    iget-object v8, v0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    goto :goto_6

    :cond_6
    move-object/from16 v8, p7

    :goto_6
    and-int/lit16 v9, v1, 0x80

    if-eqz v9, :cond_7

    iget-object v9, v0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    goto :goto_7

    :cond_7
    move-object/from16 v9, p8

    :goto_7
    and-int/lit16 v10, v1, 0x100

    if-eqz v10, :cond_8

    iget-object v10, v0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    goto :goto_8

    :cond_8
    move-object/from16 v10, p9

    :goto_8
    and-int/lit16 v11, v1, 0x200

    if-eqz v11, :cond_9

    iget-object v11, v0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    goto :goto_9

    :cond_9
    move-object/from16 v11, p10

    :goto_9
    and-int/lit16 v12, v1, 0x400

    if-eqz v12, :cond_a

    iget-object v12, v0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    goto :goto_a

    :cond_a
    move-object/from16 v12, p11

    :goto_a
    and-int/lit16 v13, v1, 0x800

    if-eqz v13, :cond_b

    iget-object v13, v0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    goto :goto_b

    :cond_b
    move-object/from16 v13, p12

    :goto_b
    and-int/lit16 v14, v1, 0x1000

    if-eqz v14, :cond_c

    iget v14, v0, Lcom/abox/apps/model/UserInfo;->userId:I

    goto :goto_c

    :cond_c
    move/from16 v14, p13

    :goto_c
    and-int/lit16 v15, v1, 0x2000

    if-eqz v15, :cond_d

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    goto :goto_d

    :cond_d
    move-object/from16 v15, p14

    :goto_d
    move-object/from16 p14, v15

    and-int/lit16 v15, v1, 0x4000

    if-eqz v15, :cond_e

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    goto :goto_e

    :cond_e
    move-object/from16 v15, p15

    :goto_e
    const v16, 0x8000

    and-int v16, v1, v16

    move/from16 p13, v14

    move-object/from16 p15, v15

    if-eqz v16, :cond_f

    iget-wide v14, v0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    goto :goto_f

    :cond_f
    move-wide/from16 v14, p16

    :goto_f
    const/high16 v16, 0x10000

    and-int v16, v1, v16

    move-wide/from16 p16, v14

    if-eqz v16, :cond_10

    iget-object v14, v0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    goto :goto_10

    :cond_10
    move-object/from16 v14, p18

    :goto_10
    const/high16 v15, 0x20000

    and-int/2addr v15, v1

    if-eqz v15, :cond_11

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    goto :goto_11

    :cond_11
    move-object/from16 v15, p19

    :goto_11
    const/high16 v16, 0x40000

    and-int v16, v1, v16

    move-object/from16 p19, v15

    if-eqz v16, :cond_12

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    goto :goto_12

    :cond_12
    move-object/from16 v15, p20

    :goto_12
    const/high16 v16, 0x80000

    and-int v16, v1, v16

    move-object/from16 p20, v15

    if-eqz v16, :cond_13

    iget-boolean v15, v0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    goto :goto_13

    :cond_13
    move/from16 v15, p21

    :goto_13
    const/high16 v16, 0x100000

    and-int v16, v1, v16

    move/from16 p21, v15

    if-eqz v16, :cond_14

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    goto :goto_14

    :cond_14
    move-object/from16 v15, p22

    :goto_14
    const/high16 v16, 0x200000

    and-int v16, v1, v16

    move-object/from16 p22, v15

    if-eqz v16, :cond_15

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    goto :goto_15

    :cond_15
    move-object/from16 v15, p23

    :goto_15
    const/high16 v16, 0x400000

    and-int v16, v1, v16

    move-object/from16 p23, v15

    if-eqz v16, :cond_16

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    goto :goto_16

    :cond_16
    move-object/from16 v15, p24

    :goto_16
    const/high16 v16, 0x800000

    and-int v16, v1, v16

    move-object/from16 p18, v14

    move-object/from16 p24, v15

    if-eqz v16, :cond_17

    iget-wide v14, v0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    goto :goto_17

    :cond_17
    move-wide/from16 v14, p25

    :goto_17
    const/high16 v16, 0x1000000

    and-int v16, v1, v16

    move-wide/from16 p25, v14

    if-eqz v16, :cond_18

    iget v14, v0, Lcom/abox/apps/model/UserInfo;->userState:I

    goto :goto_18

    :cond_18
    move/from16 v14, p27

    :goto_18
    const/high16 v15, 0x2000000

    and-int/2addr v15, v1

    if-eqz v15, :cond_19

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    goto :goto_19

    :cond_19
    move-object/from16 v15, p28

    :goto_19
    const/high16 v16, 0x4000000

    and-int v16, v1, v16

    move-object/from16 p28, v15

    if-eqz v16, :cond_1a

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    goto :goto_1a

    :cond_1a
    move-object/from16 v15, p29

    :goto_1a
    const/high16 v16, 0x8000000

    and-int v16, v1, v16

    move-object/from16 p29, v15

    if-eqz v16, :cond_1b

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    goto :goto_1b

    :cond_1b
    move-object/from16 v15, p30

    :goto_1b
    const/high16 v16, 0x10000000

    and-int v16, v1, v16

    move-object/from16 p30, v15

    if-eqz v16, :cond_1c

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    goto :goto_1c

    :cond_1c
    move-object/from16 v15, p31

    :goto_1c
    const/high16 v16, 0x20000000

    and-int v16, v1, v16

    move-object/from16 p31, v15

    if-eqz v16, :cond_1d

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    goto :goto_1d

    :cond_1d
    move-object/from16 v15, p32

    :goto_1d
    const/high16 v16, 0x40000000    # 2.0f

    and-int v1, v1, v16

    if-eqz v1, :cond_1e

    iget-object v1, v0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    goto :goto_1e

    :cond_1e
    move-object/from16 v1, p33

    :goto_1e
    move/from16 p1, v2

    move-object/from16 p2, v3

    move-object/from16 p3, v4

    move-object/from16 p4, v5

    move-object/from16 p5, v6

    move-object/from16 p6, v7

    move-object/from16 p7, v8

    move-object/from16 p8, v9

    move-object/from16 p9, v10

    move-object/from16 p10, v11

    move-object/from16 p11, v12

    move-object/from16 p12, v13

    move/from16 p27, v14

    move-object/from16 p32, v15

    move-object/from16 p33, v1

    invoke-virtual/range {p0 .. p33}, Lcom/abox/apps/model/UserInfo;->copy(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/abox/apps/model/UserInfo;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public final component1()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->appId:I

    return v0
.end method

.method public final component10()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    return-object v0
.end method

.method public final component11()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    return-object v0
.end method

.method public final component12()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    return-object v0
.end method

.method public final component13()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->userId:I

    return v0
.end method

.method public final component14()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final component15()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final component16()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    return-wide v0
.end method

.method public final component17()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    return-object v0
.end method

.method public final component18()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    return-object v0
.end method

.method public final component19()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    return-object v0
.end method

.method public final component2()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    return-object v0
.end method

.method public final component20()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    return v0
.end method

.method public final component21()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    return-object v0
.end method

.method public final component22()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    return-object v0
.end method

.method public final component23()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    return-object v0
.end method

.method public final component24()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    return-wide v0
.end method

.method public final component25()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->userState:I

    return v0
.end method

.method public final component26()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    return-object v0
.end method

.method public final component27()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    return-object v0
.end method

.method public final component28()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    return-object v0
.end method

.method public final component29()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    return-object v0
.end method

.method public final component3()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    return-object v0
.end method

.method public final component30()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    return-object v0
.end method

.method public final component31()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    return-object v0
.end method

.method public final component4()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    return-object v0
.end method

.method public final component5()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    return-object v0
.end method

.method public final component6()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    return-object v0
.end method

.method public final component7()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    return-object v0
.end method

.method public final component8()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    return-object v0
.end method

.method public final component9()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    return-object v0
.end method

.method public final copy(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/abox/apps/model/UserInfo;
    .locals 35
    .param p2    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p9    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p10    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p12    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p14    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p15    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p18    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p19    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p20    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p22    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p23    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p24    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p28    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p29    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p30    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p31    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p32    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .param p33    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    move/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    move-object/from16 v6, p6

    move-object/from16 v7, p7

    move-object/from16 v8, p8

    move-object/from16 v9, p9

    move-object/from16 v10, p10

    move-object/from16 v11, p11

    move-object/from16 v12, p12

    move/from16 v13, p13

    move-object/from16 v14, p14

    move-object/from16 v15, p15

    move-wide/from16 v16, p16

    move-object/from16 v18, p18

    move-object/from16 v19, p19

    move-object/from16 v20, p20

    move/from16 v21, p21

    move-object/from16 v22, p22

    move-object/from16 v23, p23

    move-object/from16 v24, p24

    move-wide/from16 v25, p25

    move/from16 v27, p27

    move-object/from16 v28, p28

    move-object/from16 v29, p29

    move-object/from16 v30, p30

    move-object/from16 v31, p31

    move-object/from16 v32, p32

    move-object/from16 v33, p33

    new-instance v34, Lcom/abox/apps/model/UserInfo;

    move-object/from16 v0, v34

    invoke-direct/range {v0 .. v33}, Lcom/abox/apps/model/UserInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-object v34
.end method

.method public describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lcom/abox/apps/model/UserInfo;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lcom/abox/apps/model/UserInfo;

    iget v1, p0, Lcom/abox/apps/model/UserInfo;->appId:I

    iget v3, p1, Lcom/abox/apps/model/UserInfo;->appId:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    :cond_6
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_7

    return v2

    :cond_7
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_8

    return v2

    :cond_8
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_9

    return v2

    :cond_9
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_a

    return v2

    :cond_a
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_b

    return v2

    :cond_b
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    return v2

    :cond_c
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_d

    return v2

    :cond_d
    iget v1, p0, Lcom/abox/apps/model/UserInfo;->userId:I

    iget v3, p1, Lcom/abox/apps/model/UserInfo;->userId:I

    if-eq v1, v3, :cond_e

    return v2

    :cond_e
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_f

    return v2

    :cond_f
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_10

    return v2

    :cond_10
    iget-wide v3, p0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    iget-wide v5, p1, Lcom/abox/apps/model/UserInfo;->expireDate:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_11

    return v2

    :cond_11
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_12

    return v2

    :cond_12
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_13

    return v2

    :cond_13
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_14

    return v2

    :cond_14
    iget-boolean v1, p0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    iget-boolean v3, p1, Lcom/abox/apps/model/UserInfo;->isForever:Z

    if-eq v1, v3, :cond_15

    return v2

    :cond_15
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_16

    return v2

    :cond_16
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_17

    return v2

    :cond_17
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_18

    return v2

    :cond_18
    iget-wide v3, p0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    iget-wide v5, p1, Lcom/abox/apps/model/UserInfo;->loginTime:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_19

    return v2

    :cond_19
    iget v1, p0, Lcom/abox/apps/model/UserInfo;->userState:I

    iget v3, p1, Lcom/abox/apps/model/UserInfo;->userState:I

    if-eq v1, v3, :cond_1a

    return v2

    :cond_1a
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1b

    return v2

    :cond_1b
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1c

    return v2

    :cond_1c
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1d

    return v2

    :cond_1d
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1e

    return v2

    :cond_1e
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    iget-object v3, p1, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1f

    return v2

    :cond_1f
    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    iget-object p1, p1, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_20

    return v2

    :cond_20
    return v0
.end method

.method public final getAppId()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->appId:I

    return v0
.end method

.method public final getChannel()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    return-object v0
.end method

.method public final getCostMoney()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    return-object v0
.end method

.method public final getCreateBy()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    return-object v0
.end method

.method public final getCreateTime()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    return-object v0
.end method

.method public final getDeviceInfo()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    return-object v0
.end method

.method public final getDeviceKey()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    return-object v0
.end method

.method public final getDurationTime()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    return-object v0
.end method

.method public final getEmail()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    return-object v0
.end method

.method public final getExpireDate()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    return-wide v0
.end method

.method public final getHwAccount()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    return-object v0
.end method

.method public final getHwUnionId()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    return-object v0
.end method

.method public final getLanguage()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    return-object v0
.end method

.method public final getLoginTime()J
    .locals 2

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    return-wide v0
.end method

.method public final getName()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    return-object v0
.end method

.method public final getOperation()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    return-object v0
.end method

.method public final getRegion()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    return-object v0
.end method

.method public final getRemark()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    return-object v0
.end method

.method public final getStartDate()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    return-object v0
.end method

.method public final getStatus()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    return-object v0
.end method

.method public final getToken()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    return-object v0
.end method

.method public final getUpdateBy()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    return-object v0
.end method

.method public final getUpdateTime()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    return-object v0
.end method

.method public final getUserId()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->userId:I

    return v0
.end method

.method public final getUserIdText()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    return-object v0
.end method

.method public final getUserState()I
    .locals 1

    iget v0, p0, Lcom/abox/apps/model/UserInfo;->userState:I

    return v0
.end method

.method public final getUserType()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    return-object v0
.end method

.method public final getVersionCode()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    return-object v0
.end method

.method public hashCode()I
    .locals 32

    move-object/from16 v0, p0

    iget v1, v0, Lcom/abox/apps/model/UserInfo;->appId:I

    invoke-static {v1}, Ljava/lang/Integer;->hashCode(I)I

    move-result v1

    iget-object v2, v0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    if-nez v2, :cond_0

    const/4 v2, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v2}, Ljava/lang/String;->hashCode()I

    move-result v2

    :goto_0
    iget-object v4, v0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    if-nez v4, :cond_1

    const/4 v4, 0x0

    goto :goto_1

    :cond_1
    invoke-virtual {v4}, Ljava/lang/String;->hashCode()I

    move-result v4

    :goto_1
    iget-object v5, v0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    if-nez v5, :cond_2

    const/4 v5, 0x0

    goto :goto_2

    :cond_2
    invoke-virtual {v5}, Ljava/lang/String;->hashCode()I

    move-result v5

    :goto_2
    iget-object v6, v0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    if-nez v6, :cond_3

    const/4 v6, 0x0

    goto :goto_3

    :cond_3
    invoke-virtual {v6}, Ljava/lang/String;->hashCode()I

    move-result v6

    :goto_3
    iget-object v7, v0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    if-nez v7, :cond_4

    const/4 v7, 0x0

    goto :goto_4

    :cond_4
    invoke-virtual {v7}, Ljava/lang/String;->hashCode()I

    move-result v7

    :goto_4
    iget-object v8, v0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    if-nez v8, :cond_5

    const/4 v8, 0x0

    goto :goto_5

    :cond_5
    invoke-virtual {v8}, Ljava/lang/String;->hashCode()I

    move-result v8

    :goto_5
    iget-object v9, v0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    if-nez v9, :cond_6

    const/4 v9, 0x0

    goto :goto_6

    :cond_6
    invoke-virtual {v9}, Ljava/lang/String;->hashCode()I

    move-result v9

    :goto_6
    iget-object v10, v0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    if-nez v10, :cond_7

    const/4 v10, 0x0

    goto :goto_7

    :cond_7
    invoke-virtual {v10}, Ljava/lang/String;->hashCode()I

    move-result v10

    :goto_7
    iget-object v11, v0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    if-nez v11, :cond_8

    const/4 v11, 0x0

    goto :goto_8

    :cond_8
    invoke-virtual {v11}, Ljava/lang/String;->hashCode()I

    move-result v11

    :goto_8
    iget-object v12, v0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    if-nez v12, :cond_9

    const/4 v12, 0x0

    goto :goto_9

    :cond_9
    invoke-virtual {v12}, Ljava/lang/String;->hashCode()I

    move-result v12

    :goto_9
    iget-object v13, v0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    if-nez v13, :cond_a

    const/4 v13, 0x0

    goto :goto_a

    :cond_a
    invoke-virtual {v13}, Ljava/lang/String;->hashCode()I

    move-result v13

    :goto_a
    iget v14, v0, Lcom/abox/apps/model/UserInfo;->userId:I

    invoke-static {v14}, Ljava/lang/Integer;->hashCode(I)I

    move-result v14

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    if-nez v15, :cond_b

    const/4 v15, 0x0

    goto :goto_b

    :cond_b
    invoke-virtual {v15}, Ljava/lang/Object;->hashCode()I

    move-result v15

    :goto_b
    iget-object v3, v0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    if-nez v3, :cond_c

    move/from16 v16, v14

    move/from16 v17, v15

    const/4 v3, 0x0

    goto :goto_c

    :cond_c
    invoke-virtual {v3}, Ljava/lang/Object;->hashCode()I

    move-result v3

    move/from16 v16, v14

    move/from16 v17, v15

    :goto_c
    iget-wide v14, v0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    invoke-static {v14, v15}, Ljava/lang/Long;->hashCode(J)I

    move-result v14

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    if-nez v15, :cond_d

    const/16 v18, 0x0

    goto :goto_d

    :cond_d
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v18, v15

    :goto_d
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    if-nez v15, :cond_e

    const/16 v19, 0x0

    goto :goto_e

    :cond_e
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v19, v15

    :goto_e
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    if-nez v15, :cond_f

    const/16 v20, 0x0

    goto :goto_f

    :cond_f
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v20, v15

    :goto_f
    iget-boolean v15, v0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    if-eqz v15, :cond_10

    const/4 v15, 0x1

    :cond_10
    move/from16 v21, v15

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    if-nez v15, :cond_11

    const/16 v22, 0x0

    goto :goto_10

    :cond_11
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v22, v15

    :goto_10
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    if-nez v15, :cond_12

    const/16 v23, 0x0

    goto :goto_11

    :cond_12
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v23, v15

    :goto_11
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    if-nez v15, :cond_13

    move/from16 v24, v14

    const/16 v25, 0x0

    goto :goto_12

    :cond_13
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v24, v14

    move/from16 v25, v15

    :goto_12
    iget-wide v14, v0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    invoke-static {v14, v15}, Ljava/lang/Long;->hashCode(J)I

    move-result v14

    iget v15, v0, Lcom/abox/apps/model/UserInfo;->userState:I

    invoke-static {v15}, Ljava/lang/Integer;->hashCode(I)I

    move-result v15

    move/from16 v26, v15

    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    if-nez v15, :cond_14

    const/16 v27, 0x0

    goto :goto_13

    :cond_14
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v27, v15

    :goto_13
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    if-nez v15, :cond_15

    const/16 v28, 0x0

    goto :goto_14

    :cond_15
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v28, v15

    :goto_14
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    if-nez v15, :cond_16

    const/16 v29, 0x0

    goto :goto_15

    :cond_16
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v29, v15

    :goto_15
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    if-nez v15, :cond_17

    const/16 v30, 0x0

    goto :goto_16

    :cond_17
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v30, v15

    :goto_16
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    if-nez v15, :cond_18

    const/16 v31, 0x0

    goto :goto_17

    :cond_18
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    move/from16 v31, v15

    :goto_17
    iget-object v15, v0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    if-nez v15, :cond_19

    const/4 v15, 0x0

    goto :goto_18

    :cond_19
    invoke-virtual {v15}, Ljava/lang/String;->hashCode()I

    move-result v15

    :goto_18
    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v2

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v4

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v5

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v6

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v7

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v8

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v9

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v10

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v11

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v12

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v13

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v16

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v17

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v3

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v24

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v18

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v19

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v20

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v21

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v22

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v23

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v25

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v14

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v26

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v27

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v28

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v29

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v30

    mul-int/lit8 v1, v1, 0x1f

    add-int v1, v1, v31

    mul-int/lit8 v1, v1, 0x1f

    add-int/2addr v1, v15

    return v1
.end method

.method public final isForever()Z
    .locals 1

    # Always return true to bypass VIP validation
    const/4 v0, 0x1

    return v0
.end method

.method public final isNew()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final isVip()Ljava/lang/Boolean;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    # Always return Boolean.TRUE to bypass VIP validation
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    return-object v0
.end method

.method public final setUserIdText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    return-void
.end method

.method public final setUserType(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    return-void
.end method

.method public final setVip(Ljava/lang/Boolean;)V
    .locals 0
    .param p1    # Ljava/lang/Boolean;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 3
    .annotation build Lo/cbz;
    .end annotation

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "UserInfo(appId="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/UserInfo;->appId:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", deviceKey="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", name="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", channel="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", versionCode="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", language="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", createBy="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", createTime="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", updateBy="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", updateTime="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", remark="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", status="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", userId="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/UserInfo;->userId:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", isNew="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", isVip="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", expireDate="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v1, ", email="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", userType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", userIdText="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", isForever="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, ", region="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", deviceInfo="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", hwAccount="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", loginTime="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v1, ", userState="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lcom/abox/apps/model/UserInfo;->userState:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", costMoney="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", hwUnionId="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", startDate="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", token="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", operation="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", durationTime="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public writeToParcel(Landroid/os/Parcel;I)V
    .locals 2
    .param p1    # Landroid/os/Parcel;
        .annotation build Lo/cbz;
        .end annotation
    .end param

    const-string p2, "parcel"

    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    iget p2, p0, Lcom/abox/apps/model/UserInfo;->appId:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->deviceKey:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->name:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->channel:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->versionCode:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->language:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->createBy:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->createTime:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->updateBy:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->updateTime:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->remark:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->status:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget p2, p0, Lcom/abox/apps/model/UserInfo;->userId:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->isNew:Ljava/lang/Boolean;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeValue(Ljava/lang/Object;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->isVip:Ljava/lang/Boolean;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeValue(Ljava/lang/Object;)V

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->expireDate:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->email:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->userType:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->userIdText:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-boolean p2, p0, Lcom/abox/apps/model/UserInfo;->isForever:Z

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeByte(B)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->region:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->deviceInfo:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->hwAccount:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-wide v0, p0, Lcom/abox/apps/model/UserInfo;->loginTime:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget p2, p0, Lcom/abox/apps/model/UserInfo;->userState:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->costMoney:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->hwUnionId:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->startDate:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->token:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->operation:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lcom/abox/apps/model/UserInfo;->durationTime:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    return-void
.end method
