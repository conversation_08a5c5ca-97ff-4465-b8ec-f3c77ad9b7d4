.class public final Lcom/abox/apps/activitys/RegisterActivity;
.super Lcom/abox/apps/activitys/BaseCompatActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/abox/apps/activitys/BaseCompatActivity<",
        "Lcom/abox/apps/databinding/ActivityForgotPwdBinding;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0016\u001a\u00020\u00022\u0006\u0010\u0017\u001a\u00020\u0018H\u0014J\u0008\u0010\u0019\u001a\u00020\u001aH\u0014J\u0008\u0010\u001b\u001a\u00020\u001aH\u0002R\u001c\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007\"\u0004\u0008\u0008\u0010\tR\u001a\u0010\n\u001a\u00020\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u000c\u0010\r\"\u0004\u0008\u000e\u0010\u000fR\u001b\u0010\u0010\u001a\u00020\u00118BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u001c"
    }
    d2 = {
        "Lcom/abox/apps/activitys/RegisterActivity;",
        "Lcom/abox/apps/activitys/BaseCompatActivity;",
        "Lcom/abox/apps/databinding/ActivityForgotPwdBinding;",
        "()V",
        "email",
        "",
        "getEmail",
        "()Ljava/lang/String;",
        "setEmail",
        "(Ljava/lang/String;)V",
        "firstStep",
        "",
        "getFirstStep",
        "()Z",
        "setFirstStep",
        "(Z)V",
        "viewModel",
        "Lcom/abox/apps/viewmodel/RegisterViewModel;",
        "getViewModel",
        "()Lcom/abox/apps/viewmodel/RegisterViewModel;",
        "viewModel$delegate",
        "Lkotlin/Lazy;",
        "inflateViewBinding",
        "inflater",
        "Landroid/view/LayoutInflater;",
        "onAfterViews",
        "",
        "updateLayout",
        "app_websiteRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
    value = {
        "SMAP\nRegisterActivity.kt\nKotlin\n*S Kotlin\n*F\n+ 1 RegisterActivity.kt\ncom/abox/apps/activitys/RegisterActivity\n+ 2 ActivityViewModelLazy.kt\nandroidx/activity/ActivityViewModelLazyKt\n*L\n1#1,133:1\n75#2,13:134\n*S KotlinDebug\n*F\n+ 1 RegisterActivity.kt\ncom/abox/apps/activitys/RegisterActivity\n*L\n27#1:134,13\n*E\n"
    }
.end annotation


# instance fields
.field private asBinder:Ljava/lang/String;
    .annotation build Lo/cbw;
    .end annotation
.end field

.field private getDefaultImpl:Z

.field private final setDefaultImpl:Lkotlin/Lazy;
    .annotation build Lo/cbz;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 5

    invoke-direct {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;-><init>()V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    const-string v0, ""

    iput-object v0, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    new-instance v0, Lcom/abox/apps/activitys/RegisterActivity$ActionBar;

    invoke-direct {v0, p0}, Lcom/abox/apps/activitys/RegisterActivity$ActionBar;-><init>(Landroidx/activity/ComponentActivity;)V

    const-class v1, Lcom/abox/apps/viewmodel/RegisterViewModel;

    invoke-static {v1}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v1

    new-instance v2, Lcom/abox/apps/activitys/RegisterActivity$LoaderManager;

    invoke-direct {v2, p0}, Lcom/abox/apps/activitys/RegisterActivity$LoaderManager;-><init>(Landroidx/activity/ComponentActivity;)V

    new-instance v3, Lcom/abox/apps/activitys/RegisterActivity$FragmentManager;

    const/4 v4, 0x0

    invoke-direct {v3, v4, p0}, Lcom/abox/apps/activitys/RegisterActivity$FragmentManager;-><init>(Lkotlin/jvm/functions/Function0;Landroidx/activity/ComponentActivity;)V

    new-instance v4, Landroidx/lifecycle/ViewModelLazy;

    invoke-direct {v4, v1, v2, v0, v3}, Landroidx/lifecycle/ViewModelLazy;-><init>(Lkotlin/reflect/KClass;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    iput-object v4, p0, Lcom/abox/apps/activitys/RegisterActivity;->setDefaultImpl:Lkotlin/Lazy;

    return-void
.end method

.method private final IconCompatParcelizer()V
    .locals 5

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->setInternalConnectionCallback:Landroidx/constraintlayout/widget/Group;

    iget-boolean v1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    const/16 v2, 0x8

    const/4 v3, 0x0

    if-eqz v1, :cond_0

    move v1, v3

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    invoke-virtual {v0, v1}, Landroidx/constraintlayout/widget/Group;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->MediaBrowserCompat$ItemReceiver:Landroidx/constraintlayout/widget/Group;

    iget-boolean v1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    move v2, v3

    :goto_1
    invoke-virtual {v0, v2}, Landroidx/constraintlayout/widget/Group;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->MediaDescriptionCompat:Landroid/widget/TextView;

    iget-boolean v1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    if-eqz v1, :cond_2

    sget v1, Lo/Cursor$TaskStackBuilder;->RemoteActionCompatParcelizer:I

    invoke-virtual {p0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v1

    goto :goto_2

    :cond_2
    sget v1, Lo/Cursor$TaskStackBuilder;->read:I

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    iget-object v4, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    aput-object v4, v2, v3

    invoke-virtual {p0, v1, v2}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    :goto_2
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method private static final IconCompatParcelizer(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean p1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    iput-boolean p1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->IconCompatParcelizer()V

    :goto_0
    return-void
.end method

.method private static final RemoteActionCompatParcelizer(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 2

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onTransact:Landroid/widget/EditText;

    invoke-virtual {p1}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    move p1, v0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    return-void

    :cond_2
    sget-object p1, Lo/SequenceInputStream;->getDefaultImpl:Lo/SequenceInputStream;

    iget-object v1, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1, v1}, Lo/SequenceInputStream;->onTransact(Ljava/lang/String;)Z

    move-result p1

    if-nez p1, :cond_3

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p0

    check-cast p0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p0, p0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->setDefaultImpl:Landroid/widget/TextView;

    invoke-virtual {p0, v0}, Landroid/view/View;->setVisibility(I)V

    return-void

    :cond_3
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onTransact:Landroid/widget/EditText;

    const-string v0, "emailEt"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1}, Lo/DataOutputStream;->asInterface(Landroid/view/View;)V

    sget-object p1, Lo/Animation;->getDefaultImpl:Lo/Animation;

    const/4 v0, 0x2

    const/4 v1, 0x0

    invoke-static {p1, p0, v1, v0, v1}, Lo/Animation;->getDefaultImpl(Lo/Animation;Landroid/content/Context;Ljava/lang/String;ILjava/lang/Object;)V

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->onConnected()Lcom/abox/apps/viewmodel/RegisterViewModel;

    move-result-object p1

    iget-object p0, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    invoke-static {p0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1, p0}, Lcom/abox/apps/viewmodel/RegisterViewModel;->asInterface(Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic asBinder(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->RemoteActionCompatParcelizer(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic asInterface(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->IconCompatParcelizer(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic getDefaultImpl(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->write(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V

    return-void
.end method

.method private final onConnected()Lcom/abox/apps/viewmodel/RegisterViewModel;
    .locals 1

    iget-object v0, p0, Lcom/abox/apps/activitys/RegisterActivity;->setDefaultImpl:Lkotlin/Lazy;

    invoke-interface {v0}, Lkotlin/Lazy;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/viewmodel/RegisterViewModel;

    return-object v0
.end method

.method private static final onConnected(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 2

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lo/SequenceInputStream;->getDefaultImpl:Lo/SequenceInputStream;

    sget v0, Lo/Cursor$TaskStackBuilder;->emit:I

    invoke-virtual {p0, v0}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    const-string v1, "getString(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, p0, v0}, Lo/SequenceInputStream;->setDefaultImpl(Landroid/content/Context;Ljava/lang/String;)V

    return-void
.end method

.method public static final synthetic onTransact(Lcom/abox/apps/activitys/RegisterActivity;)V
    .locals 0

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->IconCompatParcelizer()V

    return-void
.end method

.method public static synthetic onTransact(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->read(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final read(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 2

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object p1, Lo/SequenceInputStream;->getDefaultImpl:Lo/SequenceInputStream;

    sget v0, Lo/Cursor$TaskStackBuilder;->fullyDrawnReported:I

    invoke-virtual {p0, v0}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    const-string v1, "getString(...)"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1, p0, v0}, Lo/SequenceInputStream;->setDefaultImpl(Landroid/content/Context;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic setDefaultImpl(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->onConnected(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V

    return-void
.end method

.method private static final write(Lcom/abox/apps/activitys/RegisterActivity;Landroid/view/View;)V
    .locals 4

    const-string/jumbo p1, "this$0"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->MediaBrowserCompat$MediaItem:Landroid/widget/EditText;

    invoke-virtual {p1}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onConnectionFailed:Landroid/widget/EditText;

    invoke-virtual {v0}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    if-nez v1, :cond_2

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v3, 0x6

    if-ge v1, v3, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onConnectionFailed:Landroid/widget/EditText;

    const-string v2, "pwdEt"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v1}, Lo/DataOutputStream;->asInterface(Landroid/view/View;)V

    sget-object v1, Lo/Animation;->getDefaultImpl:Lo/Animation;

    const/4 v2, 0x2

    const/4 v3, 0x0

    invoke-static {v1, p0, v3, v2, v3}, Lo/Animation;->getDefaultImpl(Lo/Animation;Landroid/content/Context;Ljava/lang/String;ILjava/lang/Object;)V

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->onConnected()Lcom/abox/apps/viewmodel/RegisterViewModel;

    move-result-object v1

    iget-object p0, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    invoke-static {p0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v1, p0, v0, p1}, Lcom/abox/apps/viewmodel/RegisterViewModel;->getDefaultImpl(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_2
    :goto_1
    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p1

    check-cast p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p1, p1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->IconCompatParcelizer:Landroid/widget/TextView;

    sget v0, Lo/Cursor$TaskStackBuilder;->addCallback:I

    invoke-virtual {p0, v0}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object p0

    check-cast p0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object p0, p0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->IconCompatParcelizer:Landroid/widget/TextView;

    invoke-virtual {p0, v2}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method


# virtual methods
.method public final RemoteActionCompatParcelizer()Z
    .locals 1

    iget-boolean v0, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    return v0
.end method

.method public final asBinder(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/abox/apps/activitys/RegisterActivity;->getDefaultImpl:Z

    return-void
.end method

.method protected onTransact(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityForgotPwdBinding;
    .locals 2
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lo/cbz;
        .end annotation
    .end param
    .annotation build Lo/cbz;
    .end annotation

    const-string v0, "inflater"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-static {p1, v0, v1}, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->asInterface(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    move-result-object p1

    const-string v0, "inflate(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method protected onTransact()V
    .locals 3

    sget-object v0, Lo/UnicodeBlock;->asInterface:Lo/UnicodeBlock;

    const-string v1, "enterCreatePage"

    invoke-virtual {v0, v1}, Lo/UnicodeBlock;->asInterface(Ljava/lang/String;)V

    sget-object v0, Lo/Serializable;->setDefaultImpl:Lo/Serializable;

    invoke-virtual {v0, v1}, Lo/SerializablePermission;->asInterface(Ljava/lang/String;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onConnected:Landroid/widget/TextView;

    sget v1, Lo/Cursor$TaskStackBuilder;->Api26Impl$$ExternalSyntheticApiModelOutline0:I

    invoke-virtual {p0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->MediaDescriptionCompat:Landroid/widget/TextView;

    sget v1, Lo/Cursor$TaskStackBuilder;->RemoteActionCompatParcelizer:I

    invoke-virtual {p0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->getDefaultImpl:Landroid/widget/ImageView;

    new-instance v1, Lo/AttributeSet;

    invoke-direct {v1, p0}, Lo/AttributeSet;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->asInterface:Landroid/widget/Button;

    new-instance v1, Lo/Property;

    invoke-direct {v1, p0}, Lo/Property;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->RemoteActionCompatParcelizer:Landroid/widget/TextView;

    new-instance v1, Lo/Display;

    invoke-direct {v1, p0}, Lo/Display;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onConnectionSuspended:Landroid/widget/TextView;

    new-instance v1, Lo/ContextThemeWrapper;

    invoke-direct {v1, p0}, Lo/ContextThemeWrapper;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v0

    check-cast v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v0, v0, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->write:Landroid/widget/Button;

    new-instance v1, Lo/ActionMode;

    invoke-direct {v1, p0}, Lo/ActionMode;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    new-instance v0, Lcom/abox/apps/activitys/RegisterActivity$StateListAnimator;

    invoke-direct {v0, p0}, Lcom/abox/apps/activitys/RegisterActivity$StateListAnimator;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onTransact:Landroid/widget/EditText;

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->addTextChangedListener(Landroid/text/TextWatcher;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->MediaBrowserCompat$MediaItem:Landroid/widget/EditText;

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->addTextChangedListener(Landroid/text/TextWatcher;)V

    invoke-virtual {p0}, Lcom/abox/apps/activitys/BaseCompatActivity;->asBinder()Landroidx/viewbinding/ViewBinding;

    move-result-object v1

    check-cast v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    iget-object v1, v1, Lcom/abox/apps/databinding/ActivityForgotPwdBinding;->onConnectionFailed:Landroid/widget/EditText;

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->addTextChangedListener(Landroid/text/TextWatcher;)V

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->IconCompatParcelizer()V

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->onConnected()Lcom/abox/apps/viewmodel/RegisterViewModel;

    move-result-object v0

    invoke-virtual {v0}, Lcom/abox/apps/viewmodel/RegisterViewModel;->asInterface()Landroidx/lifecycle/MutableLiveData;

    move-result-object v0

    new-instance v1, Lcom/abox/apps/activitys/RegisterActivity$Application;

    new-instance v2, Lcom/abox/apps/activitys/RegisterActivity$TaskDescription;

    invoke-direct {v2, p0}, Lcom/abox/apps/activitys/RegisterActivity$TaskDescription;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-direct {v1, v2}, Lcom/abox/apps/activitys/RegisterActivity$Application;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/LiveData;->observe(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V

    invoke-direct {p0}, Lcom/abox/apps/activitys/RegisterActivity;->onConnected()Lcom/abox/apps/viewmodel/RegisterViewModel;

    move-result-object v0

    invoke-virtual {v0}, Lcom/abox/apps/viewmodel/RegisterViewModel;->setDefaultImpl()Landroidx/lifecycle/MutableLiveData;

    move-result-object v0

    new-instance v1, Lcom/abox/apps/activitys/RegisterActivity$Application;

    new-instance v2, Lcom/abox/apps/activitys/RegisterActivity$Activity;

    invoke-direct {v2, p0}, Lcom/abox/apps/activitys/RegisterActivity$Activity;-><init>(Lcom/abox/apps/activitys/RegisterActivity;)V

    invoke-direct {v1, v2}, Lcom/abox/apps/activitys/RegisterActivity$Application;-><init>(Lkotlin/jvm/functions/Function1;)V

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/LiveData;->observe(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V

    return-void
.end method

.method public synthetic setDefaultImpl(Landroid/view/LayoutInflater;)Landroidx/viewbinding/ViewBinding;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/abox/apps/activitys/RegisterActivity;->onTransact(Landroid/view/LayoutInflater;)Lcom/abox/apps/databinding/ActivityForgotPwdBinding;

    move-result-object p1

    return-object p1
.end method

.method public final setDefaultImpl()Ljava/lang/String;
    .locals 1
    .annotation build Lo/cbw;
    .end annotation

    iget-object v0, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    return-object v0
.end method

.method public final setDefaultImpl(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lo/cbw;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/abox/apps/activitys/RegisterActivity;->asBinder:Ljava/lang/String;

    return-void
.end method
